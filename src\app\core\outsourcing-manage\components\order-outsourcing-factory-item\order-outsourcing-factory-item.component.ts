import { Component, Input, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON>, ViewChildren, <PERSON><PERSON><PERSON>ist, ViewChild, ChangeDetectorRef } from '@angular/core';
import { Subscription } from 'rxjs';
import { FormGroup } from '@angular/forms';
import { TranslatePipe } from '@ngx-translate/core';
import { TranslateService } from '@ngx-translate/core';
import { FlcColorSizeTableChangeEvent, FlcColorSizeTableComponent } from 'fl-common-lib';
import { IoBasic, Line, Po } from '../models/order-outsourcing-interface';
import { OrderOutsourcingService } from '../order-outsourcing.service';
import {
  ExtraProcessInfoInterface,
  SecProcessDetailInfoInterface,
} from '../../sec-process-outsourcing/model/sec-process-outsourcing.interface';
import { OrderStatus } from '../../garment-outsourcing/models/order-garment-outsourcing.enum';
import { OrderOutsourcingTabsContainerComponent } from '../order-outsourcing-tabs-container/order-outsourcing-tabs-container.component';
import { NzMessageService } from 'ng-zorro-antd/message';
import { OrderOutsourcingFactoryItemHeaderComponent } from '../order-outsourcing-factory-item-header/order-outsourcing-factory-item-header.component';

@Component({
  selector: 'app-order-outsourcing-factory-item',
  templateUrl: './order-outsourcing-factory-item.component.html',
  styleUrls: ['./order-outsourcing-factory-item.component.scss'],
  providers: [TranslatePipe],
})
export class OrderOutsourcingFactoryItemComponent implements OnInit, OnDestroy {
  @ViewChildren('colorSizePosTable') colorSizePosTableRefs!: QueryList<FlcColorSizeTableComponent>;
  @ViewChildren('colorSizeSurplusRefs') colorSizeSurplusRefs!: QueryList<FlcColorSizeTableComponent>;
  @ViewChild('tabContainer') tabContainer!: OrderOutsourcingTabsContainerComponent;
  @ViewChild('factoryItemHeader') factoryItemHeader!: OrderOutsourcingFactoryItemHeaderComponent;

  @Input() editMode!: 'add' | 'edit' | 'read'; // 当前页面编辑状态
  @Input() tableCanEdit = false;
  @Input() detailForm: any;
  @Input() parentGroup!: FormGroup;
  @Input() orderDetail!: SecProcessDetailInfoInterface;
  @Input() factoryIndex!: number; //  记录当前是第几个加工厂
  @Input() labelName = ''; // 显示外发厂文案还是加工厂文案
  @Input() tabs!: Array<Po>; // 当前IO下所有的交付单
  @Input() orderStatus!: number;
  @Input() showOrderFinish = true; // 成衣外发设置权限时，是否展示订单完成按钮
  @Input() showOneClickInbound = false; // 成衣外发设置权限时，是否展示一键入库按钮
  @Input() showAdvancePayment = false; // 成衣外发设置权限时，是否展示预付款按钮
  @Input() io_basic?: IoBasic;
  @Input() set _toggle(value: boolean) {
    this.toggle = value ?? true;
  }
  @Input() set surplusPos(value: Array<Po>) {
    // 剩余未分配的po
    this._surplusPos = value;
    this.getTabsSurplusPos();
  }
  get surplusPos() {
    return this._surplusPos;
  }
  _surplusPos!: Array<Po>;
  _tabsSurplusPos!: Array<Po>; // 添加交付单 可以选择那些交付单
  orderStatusEnum = OrderStatus;
  setPos(value: Array<Po>) {
    this._pos = value;
    this.getTabsSurplusPos();
  }
  setPoLines(value: Array<Line>) {
    this._po_lines = value;
  }
  setToggle(value: boolean) {
    this.toggle = value;
  }
  setSurplusPos(value: Array<Po>) {
    this._surplusPos = value;
    this.getTabsSurplusPos();
  }
  // 获取可以添加的交付单（基于原始pos数据，不依赖剩余未分配）
  getAvailablePos(): Array<Po> {
    if (!this.tabs || !Array.isArray(this.tabs)) {
      return [];
    }

    const result: Array<Po> = [];
    // 基于原始tabs数据计算可添加的交付单
    this.tabs.forEach((tab_po: Po) => {
      let hasPo = false;
      this._pos.forEach((po: Po) => {
        if (po.po_basic?.id === tab_po.po_basic?.id) {
          hasPo = true;
        }
      });
      if (!hasPo) {
        result.push(tab_po);
      }
    });
    return result;
  }

  // 获取可以添加的交付单（保留原方法以兼容其他功能）
  getTabsSurplusPos() {
    this._po_lines = this.outsourcingService.serialLines(this.outsourcingService.getLines(this._pos));
    const result: Array<Po> = [];
    this._surplusPos.forEach((s_po: Po) => {
      let hasPo = false;
      this._pos.forEach((po: Po) => {
        if (po.po_basic.id === s_po.po_basic.id) {
          hasPo = true;
        }
      });
      if (!hasPo) {
        result.push(s_po);
      }
    });
    this._tabsSurplusPos = result;
  }
  _pos: any = []; // 组件内部用于渲染tabs,即用户为加工厂添加的交付单
  _po_lines: any = []; // 组件内部用于渲染合计，即用户为加工厂选择的所有交付单
  secProcessEventEmitter?: Subscription;
  extra_process_info?: ExtraProcessInfoInterface;
  constructor(
    private outsourcingService: OrderOutsourcingService,
    private translateService: TranslateService,
    private translatePipe: TranslatePipe,
    private orderOutsourcingService: OrderOutsourcingService,
    private cd: ChangeDetectorRef,
    private _message: NzMessageService
  ) {}
  // 记录上一次加工厂的数据 用于重复选择加工厂回复数据
  preFactoryValue: any = null;
  setPreValue(value: any) {
    this.preFactoryValue = { ...value };
  }
  ngOnInit() {
    this.preFactoryValue = { ...this.parentGroup.value };
  }
  ngOnDestroy(): void {
    this.secProcessEventEmitter?.unsubscribe();
  }
  // 切换加工厂
  handleChangeValue(event: any) {
    if (event.value) {
      const { ss_factory_code, factory_name, label, code, value } =
        event?.data?.option_list?.find((v: any) => v.label === event.value) || {};
      this.parentGroup.get('factory_code')?.setValue(ss_factory_code);
      this.parentGroup.get('factory_name')?.setValue(factory_name);
      this.parentGroup.get('factory_archive_code')?.setValue(code);
      this.parentGroup.get('factory_archive_id')?.setValue(value);
      this.parentGroup.get('factory_short_name')?.setValue(label, { emitModelToViewChange: false });
    } else {
      this.parentGroup.get('factory_code')?.setValue(null);
      this.parentGroup.get('factory_name')?.setValue(null);
      this.parentGroup.get('factory_archive_code')?.setValue(null);
      this.parentGroup.get('factory_archive_id')?.setValue(null);
      this.parentGroup.get('factory_short_name')?.setValue(null, { emitModelToViewChange: false });
    }
    this.parentGroup.get('line_no')?.setValue(null, { emitModelToViewChange: false });
    this.parentGroup.get('line_name')?.setValue(null);
    this.toggle = true;
    this.outsourcingService.secProcessEventEmitter.emit({
      onchange: 'toggle',
      factoryIndex: this.factoryIndex,
      toggle: this.toggle,
    });
    this.outsourcingService.secProcessEventEmitter.emit({
      onchange: 'factoryChange',
      factoryIndex: this.factoryIndex,
      tabIsEmpty: this._pos.length ? false : true,
      preValue: this.preFactoryValue,
      pos: this._pos,
    });
    this.outsourcingService.garmentEventEmitter.emit({
      onchange: 'factoryChange',
      factoryIndex: this.factoryIndex,
      tabIsEmpty: this._pos.length ? false : true,
      preValue: this.preFactoryValue,
    });
  }

  // 切换产线
  handleChangeLineValue() {
    this.outsourcingService.garmentEventEmitter.emit({
      onchange: 'factoryChange',
      factoryIndex: this.factoryIndex,
      tabIsEmpty: this._pos.length ? false : true,
      preValue: this.preFactoryValue,
    });
  }

  toggle = true; // 展开收起控制 true:展开状态显示"收起", false：收起状态 ,显示"展开"
  // Tool
  handleChangeToggle() {
    this.toggle = !this.toggle;
    this.outsourcingService.secProcessEventEmitter.emit({
      onchange: 'toggle',
      factoryIndex: this.factoryIndex,
      toggle: this.toggle,
    });
  }

  // 添加一个交付单
  handleAddPo() {
    // 移除对_tabsSurplusPos的依赖，只检查基本条件
    //  || !this._tabsSurplusPos.length
    if (this.parentGroup.get('finish')?.value || this.io_basic?.is_use_plan) return;

    // 如果有空的交付单 则提示用户先填写空的交付单
    if (
      this._pos.some((po: Po) => {
        return !po.po_basic?.id;
      })
    ) {
      this._message.error(this.translateKey('outsourcingComponents.你还有未选择交付单号的交付单'));
      return;
    }

    // 检查是否还有可添加的交付单（基于原始pos数据）
    const availablePos = this.getAvailablePos();
    if (!availablePos.length) {
      this._message.error(this.translateKey('outsourcingMessage.暂无数据'));
      return;
    }

    this._pos = [
      ...this._pos,
      {
        po_basic: { deletable: true },
        po_lines: [],
      },
    ];
    // 新增po 切换tab到新的po单上
    this.tabContainer.selectedIndex = this._pos.length - 1;
  }
  ngAfterViewInit() {
    this.cd.detectChanges();
  }
  // 添加剩余未分配数据
  handleAddSurplus() {
    if (this.parentGroup.get('finish')?.value || !this.surplusPos.length || this.io_basic?.is_use_plan) return;
    // 去除空的po单
    this._pos = this._pos.filter((po: Po) => {
      return po?.po_basic?.id;
    });
    if (this.outsourcingService.surplusLines.length) {
      this._pos = this.outsourcingService.serialPos(
        this.outsourcingService.serialNumberLines([...this.mergeSurplus([...this._pos], [...this.surplusPos])], false)
      );
      this._po_lines = this.orderOutsourcingService.getSurplusLines(this.outsourcingService.deepclone(this._pos));
      this.parentGroup.get('pos')?.setValue(this._pos);
      this.parentGroup.get('lines')?.setValue(this.orderOutsourcingService.getLines(this._pos));
      this.outsourcingService.resetSurplusPos({ value: [], type: 'reset' });
      this.outsourcingService.secProcessEventEmitter.emit({
        onchange: 'poChange',
        factoryIndex: this.factoryIndex,
        mode: 'addSurplus',
      });
      // 成衣外发也需要发送garmentEventEmitter事件
      this.outsourcingService.garmentEventEmitter.emit({
        onchange: 'poChange',
        factoryIndex: this.factoryIndex,
        mode: 'addSurplus',
      });
    }

    this.outsourcingService.garmentEventEmitter.emit({
      onchange: 'outsourcingQtyChange', // 成衣外发，外发数量变化,重新计算预付款金额
    });
  }
  // _pos合并 剩余未分配的数据
  mergeSurplus(pos: Array<Po>, surplusPos: Array<Po>): Array<Po> {
    const result: Array<Po> = [];
    // 合并po
    pos.forEach((po: Po) => {
      surplusPos.forEach((s_po: Po) => {
        if (po.po_basic.id === s_po.po_basic.id) {
          s_po.po_lines.forEach((s_line: Line) => {
            let hasLine = false;
            po.po_lines.forEach((line: Line) => {
              if (line.id === s_line.id) {
                hasLine = true;
                line.qty = (line.qty || 0) + ((s_line.qty || 0) > 0 ? s_line.qty : 0);
              }
            });
            // 合并lines
            if (!hasLine) {
              po.po_lines.push(s_line);
            }
          });
          po.po_lines = [...po.po_lines];
        }
      });

      result.push(po);
    });
    // 合并po,向pos中添加pos中没有surplusPos含有的po
    surplusPos.forEach((s_po: Po) => {
      const po = result.find((po: Po) => po.po_basic.id === s_po.po_basic.id);
      if (!po) {
        s_po.po_lines.forEach((s_line: Line) => {
          s_line.qty = s_line.qty < 0 ? null : s_line.qty;
        });
        result.push(s_po);
      }
    });
    // 返回的时候重新排序
    return this.outsourcingService.getPoAllLine(result);
  }
  // tabs里面删除了一个交付单 那么向父组件更新剩余未分配数据
  surplusPosChange(value: any) {
    this.outsourcingService.resetSurplusPos(value);
  }
  // 删除一个加工厂
  handleRemoveFactory() {
    if (this.parentGroup.value.deletable === false) {
      this._message.error(this.translateKey('outsourcingComponents.工厂已接单，无法删除'));
      return;
    }
    const title = `${this.translateKey('outsourcingMessage.确定')}<span class="red-mark">${this.translateService.instant(
      'btn.delete'
    )}</span>${this.translateKey('outsourcingMessage.当前工厂？')}`;
    this.outsourcingService.confirmDialog(title).afterClose.subscribe((val) => {
      if (val) {
        // 去除空的po单
        this._pos = this.outsourcingService.filterQtyNull([...this._pos]);
        this._pos = this._pos.filter((po: Po) => {
          return po?.po_basic?.id && po.po_lines.length;
        });
        this.detailForm.controls.factorys.removeAt(this.factoryIndex);
        this.outsourcingService.resetSurplusPos({ value: this.outsourcingService.filterQtyNull([...this._pos]), type: 'merge' });
        // 删除的加工厂id 收集起来
        this.detailForm
          .get('delete_factory_infos')
          .setValue([...(this.detailForm.get('delete_factory_infos')?.value || []), this.parentGroup.get('factory_id')?.value]);
        this.outsourcingService.secProcessEventEmitter.emit({
          onchange: 'deleteFactory',
          factoryIndex: this.factoryIndex,
          factoryId: this.parentGroup.get('factory_id')?.value,
        });
      }
    });
  }
  // 尺码颜色变更
  sizeColorChange(event: FlcColorSizeTableChangeEvent) {
    // 删除单元格，行，列
    if (event.eventType === 'updateCell' || event.eventType === 'removeColumn' || event.eventType === 'removeRow') {
      const lines: any = event.affectedData;
      // 删除_pos中的line
      const surplusPos: any = [];
      this._pos.forEach((po: Po) => {
        const obj: Po = { po_basic: po.po_basic, po_lines: [] };
        po.po_lines.forEach((line: Line) => {
          lines?.forEach((cell: Line) => {
            if (cell.id === line.id || cell.id === line.po_line_id) {
              const _q = event.eventType === 'updateCell' ? cell.qty : null;
              obj.po_lines.push({ ...line, qty: _q });
              // detail页面中的refeshdata方法会拿当前的_pos浅拷贝一次以更新数据 所以此处删除后需要将数据qty = null
              //   => detail页面中更新数据为最新数据，解决未接单加工厂 在大货单中将数值修改为0，删除不掉问题。
              line.qty = _q;
            }
          });
        });
        if (obj.po_lines.length) {
          // 修复在已结单的加工厂A中 Po1添加数据 在删除数据。在加工厂B中添加po1，不显示x按钮图标
          const result = this.outsourcingService.deepclone(obj);
          this.outsourcingService.originPos.forEach((e) => {
            result.po_lines.forEach((line: any) => {
              const tmp = e.po_lines.find((l: any) => l.line_uuid === line.line_uuid);
              if (tmp) {
                line.qty = tmp.qty - line.qty;
              }
            });
          });
          result.po_basic.deletable = true;
          surplusPos.push(result);
        }
      });

      // this.orderOutsourcingService.setSurplusPos([...surplusPos]);
      // this.orderOutsourcingService.surplusLines = this.outsourcingService.serialLines(
      //   this.outsourcingService.getLines(this.outsourcingService.surplusPos)
      // );
      this.orderOutsourcingService.changeValue.emit({
        type: 'sizeChange',
      });
      this.updatePoLines();
    }
  }
  // 更新当前组件_po_lines 合计部分内容
  updatePoLines() {
    this._po_lines = this.outsourcingService.serialLines(this.outsourcingService.getLines(this._pos));
    this.parentGroup.get('lines')?.setValue(this.outsourcingService.getLines(this._pos).filter((line: Line) => line.qty));
    this.parentGroup.get('pos')?.setValue(this._pos);
    this.orderOutsourcingService.secProcessEventEmitter.emit({
      onchange: 'poChange',
      factoryIndex: this.factoryIndex,
      mode: 'colorSizeChange',
    });
    this.orderOutsourcingService.garmentEventEmitter.emit({
      onchange: 'poChange',
    });

    this.outsourcingService.garmentEventEmitter.emit({
      onchange: 'outsourcingQtyChange', // 成衣外发，外发数量变化,重新计算预付款金额
    });
  }

  // 更新颜色尺码数据
  onFetchCellData: any = (val: any) => {
    const lines: any = [];
    if (val?.sizeId && val?.colorId) {
      const originPos = this.orderOutsourcingService.deepclone(this.orderOutsourcingService.surplusPos);
      originPos.forEach((po: Po) => {
        po.po_lines.forEach((line: Line) => {
          if (line?.color_info?.color_id === val.colorId && line?.size_info?.spec_id === val.sizeId && line?.po_id === this.poId) {
            let q: number;
            this._pos.forEach((p: any) => {
              p.po_lines.forEach((c: any) => {
                if (c.id === line.id && c.po_id === line.po_id) {
                  q = c.qty;
                  c.qty = line.qty + c.qty < 0 ? 0 : line.qty + c.qty;
                  q = q - c.qty;
                }
              });
            });
            this.orderOutsourcingService.surplusPos.forEach((p: any) => {
              p.po_lines.forEach((c: any) => {
                if (c.id === line.id && c.po_id === line.po_id) c.qty = c.qty + q;
              });
            });
            this.updatePoLines();
            lines.push({ ...line });
            line.qty = null;
          }
        });
      });
      this.orderOutsourcingService.refreshSurplusPos();
    }
    // 恢复列
    if (val?.sizeId && !val?.colorId) {
      const originPos = this.orderOutsourcingService.deepclone(this.orderOutsourcingService.surplusPos);
      originPos.forEach((po: Po) => {
        po.po_lines.forEach((line: Line) => {
          if (line?.size_info?.spec_id === val.sizeId && line?.po_id === this.poId) {
            let q: number;
            this._pos.forEach((p: any) => {
              p.po_lines.forEach((c: any) => {
                if (c.id === line.id && c.po_id === line.po_id) {
                  q = c.qty;
                  c.qty = line.qty + c.qty < 0 ? 0 : line.qty + c.qty;
                  q = q - c.qty;
                }
              });
            });
            this.orderOutsourcingService.surplusPos.forEach((p: any) => {
              p.po_lines.forEach((c: any) => {
                if (c.id === line.id && c.po_id === line.po_id) c.qty = c.qty + q;
              });
            });
            this.updatePoLines();
            lines.push({ ...line });
            // line.qty = null;
          }
        });
      });
      this.orderOutsourcingService.refreshSurplusPos();
    }
    // 恢复行
    if (val?.colorId && !val?.sizeId) {
      const originPos = this.orderOutsourcingService.deepclone(this.orderOutsourcingService.surplusPos);
      originPos.forEach((po: Po) => {
        po.po_lines.forEach((line: Line) => {
          if (line?.color_info?.color_id === val.colorId && line?.po_id === this.poId) {
            let q: number;
            this._pos.forEach((p: any) => {
              p.po_lines.forEach((c: any) => {
                if (c.id === line.id) {
                  q = c.qty;
                  c.qty = line.qty + c.qty < 0 ? 0 : line.qty + c.qty;
                  q = q - c.qty;
                }
              });
            });
            this.orderOutsourcingService.surplusPos.forEach((p: any) => {
              p.po_lines.forEach((c: any) => {
                if (c.id === line.id && c.po_id === line.po_id) c.qty = c.qty + q;
              });
            });
            this.updatePoLines();
            lines.push({ ...line });
            // line.qty = null;
          }
        });
      });
      this.orderOutsourcingService.refreshSurplusPos();
    }
    return lines;
  };
  poId!: number;
  // tab 切换获取当前tab 的po_id
  getPoId: any = (value: number) => {
    this.poId = value;
  };
  translateKey(key: string): string {
    return this.translatePipe.transform(key);
  }

  /* 成衣加工外发展示参考价 */
  showInferencePrice() {
    if (!this.parentGroup.get('factory_short_name')?.value) return;
    this.outsourcingService.garmentEventEmitter.emit({
      onchange: 'showInferencePrice',
      params: {
        style_code: this.detailForm.get('style_code').value,
        style_code_uuid: this.detailForm.get('style_code_uuid').value,
        factory_code: this.parentGroup.get('factory_code')?.value,
        factory_short_name: this.parentGroup.get('factory_short_name')?.value,
      },
    });
  }

  /* 成衣加工外发订单完成 */
  orderFinish() {
    this.outsourcingService.garmentEventEmitter.emit({
      onchange: 'orderFinish',
      id: this.parentGroup.get('factory_id')?.value,
    });
  }

  /* 大货订单一键入库 */
  oneClickInbound() {
    this.outsourcingService.garmentEventEmitter.emit({
      onchange: 'oneClickInbound',
      bulk_code: this.parentGroup.get('bulk_code')?.value,
    });
  }

  /** 大货订单预付款 */
  onAdvancePayment() {
    this.outsourcingService.garmentEventEmitter.emit({
      onchange: 'onAdvancePayment',
      bulk_code: this.parentGroup.get('bulk_code')?.value,
      factory_id: this.parentGroup.get('factory_id')?.value,
    });
  }

  con(data: any) {
    console.log(data);
  }
}
