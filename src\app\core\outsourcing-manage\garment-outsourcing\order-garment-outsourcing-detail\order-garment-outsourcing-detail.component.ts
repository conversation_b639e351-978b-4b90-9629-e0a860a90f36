import { Compo<PERSON>, OnInit, <PERSON><PERSON><PERSON><PERSON>n, Query<PERSON>ist, ChangeDetectorRef } from '@angular/core';
import { finalize, forkJoin, Subscription } from 'rxjs';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ActivatedRoute, Router } from '@angular/router';
import { CheckTypeEnum, OrderStatus } from '../models/order-garment-outsourcing.enum';
import { AbstractControl, FormArray, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { OrderOutsourcingService } from '../../components/order-outsourcing.service';
import { OrderGarmentOutsourcingService } from '../order-garment-outsourcing.service';
import { FlcColorSizeTableComponent, FlcValidatorService, FlcUtilService, FlcModalService } from 'fl-common-lib';
import { IoBasi<PERSON>, <PERSON>o<PERSON>ine, Line, Po, Pos } from '../../components/models/order-outsourcing-interface';
import { DetailInfoInterface } from '../models/order-garment-outsourcing.interface';
import { OrderOutsourcingFactoryItemComponent } from '../../components/order-outsourcing-factory-item/order-outsourcing-factory-item.component';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import { OrderOutsourcingFactoryPriceListComponent } from '../../components/order-outsourcing-factory-item/order-outsourcing-factory-price-list';
import { HttpClient } from '@angular/common/http';
import { isNotNil } from 'ng-zorro-antd/core/util';
import { isNil } from 'lodash';
import { format, startOfDay } from 'date-fns';
import { SysSettingsServices } from 'fl-sewsmart-lib/common-services';
import { NzNotificationService } from 'ng-zorro-antd/notification';

@Component({
  selector: 'app-order-garment-outsourcing-detail',
  templateUrl: './order-garment-outsourcing-detail.component.html',
  styleUrls: ['./order-garment-outsourcing-detail.component.scss'],
  providers: [TranslatePipe],
})
export class OrderGarmentOutsourcingDetailComponent implements OnInit {
  @ViewChildren('colorSizeTable') colorSizeTableRefs!: QueryList<FlcColorSizeTableComponent>;
  @ViewChildren('colorSizeSurplusTable') colorSizeTableSurplusRefs!: QueryList<FlcColorSizeTableComponent>;
  @ViewChildren('factoryItem') factoryItem!: QueryList<OrderOutsourcingFactoryItemComponent>; // 加工厂每个盒子
  detail: any; // 详情数据
  io_id!: string; // 大货单id
  order_id!: string; // 分配单id
  get topTitle() {
    return this.editMode === 'add' ? '新建大货订单' : this.editMode === 'edit' ? '编辑大货订单' : '大货订单详情';
  }
  editMode: 'add' | 'edit' | 'read' = 'read'; // 是否是编辑模式
  orderStatus: OrderStatus = 1; // 当前订单状态
  OrderStatus = OrderStatus;
  // 大货单号 form container
  searchOptionFetchUrl = this._service.bulkList;
  detailFormConfig: any = [];
  detailForm!: any;
  showMainPanel = false;
  constructor(
    private _route: ActivatedRoute,
    private _router: Router,
    private fb: FormBuilder,
    private _validator: FlcValidatorService,
    public _service: OrderGarmentOutsourcingService,
    private outsourcingService: OrderOutsourcingService,
    private _message: NzMessageService,
    private translatePipe: TranslatePipe,
    private flcUtilService: FlcUtilService,
    private cd: ChangeDetectorRef,
    private _storage: AppStorageService,
    private nzModalService: NzModalService,
    private translateService: TranslateService,
    private flcModalService: FlcModalService,
    private http: HttpClient,
    private _systemSettingService: SysSettingsServices,
    private _notifyService: NzNotificationService
  ) {}
  io_basic!: IoBasic; // io基本信息
  pos!: Pos; // po交付单所有数据
  io_lines!: Array<IoLine>; // 合计的数据

  surplusPos: Pos = this.outsourcingService.surplusPos; // 剩余未分配
  surplusLines: Array<Line> = this.outsourcingService.surplusLines; //剩余未分配合计数据
  orderDetail!: DetailInfoInterface; // 详情数据
  garmentEventEmitter!: Subscription;
  changeValueEmitter!: Subscription;
  deleteLines: Array<number> = []; // 修改未通过状态下需要删除的po_line_id
  // 强制分配给预计外发加工厂
  private _sysSubscription?: Subscription;
  onlyCanSelectOrderOption = false;
  isSpinning = false;

  ngOnInit() {
    this._service.btnArr = [...this._storage.getUserActions('outsourcing-manage/garment-outsourcing')];
    this.order_id = this._route.snapshot.paramMap.get('id') || '';
    this.io_id = this._route.snapshot.queryParamMap.get('io_id') || '';
    this.editMode = this.io_id === 'add' ? 'add' : 'read';
    this.outsourcingService.factory_type = 1;
    this.initEditorInfoDef();
    this.initEditorFormValidation();
    if (this.io_id !== 'add') {
      this.getDetail();
    }
    this.subscribeEventList();
    this.getFactorysOptions();
  }
  ngAfterViewInit() {
    this.cd.detectChanges();
  }
  subscribeEventList() {
    // 每次修改剩余未分配的数据 监听更新给当前页面组件
    this.changeValueEmitter = this.outsourcingService.changeValue.subscribe((res) => {
      if (res.type === 'sizeChange') {
        const surplusPos: any = [];

        const _originPos: Array<any> = [];
        for (let i = 0; i < this.detailForm.get('factorys').value.length; i++) {
          const _pos = this.factoryItem.get(i)!._pos;
          if (!_pos.length) continue;
          _pos.forEach((po: Po) => {
            const obj: Po = { po_basic: po.po_basic, po_lines: [] };
            po.po_lines.forEach((line: Line) => {
              obj.po_lines.push({ ...line });
            });
            if (obj.po_lines.length) {
              // 修复在已结单的加工厂A中 Po1添加数据 在删除数据。在加工厂B中添加po1，不显示x按钮图标
              const result = this.outsourcingService.deepclone(obj);
              this.outsourcingService.originPos.forEach((e) => {
                result.po_lines.forEach((line: any) => {
                  const tmp1: any = e.po_lines.find((l: any) => l.line_uuid === line.line_uuid);
                  if (tmp1) {
                    const tmp = _originPos.find((l: any) => l.line_uuid === tmp1.line_uuid) ?? tmp1;
                    line.qty = tmp.qty - line.qty;
                    line.over = line.qty < 0;
                    _originPos.push({ ...JSON.parse(JSON.stringify(line)) });
                  }
                });
              });
              result.po_basic.deletable = true;
              surplusPos.push(result);
            }
          });
        }
        this.outsourcingService.setSurplusPos([...surplusPos]);
        this.outsourcingService.surplusLines = this.outsourcingService.serialLines(
          this.outsourcingService.getLines(this.outsourcingService.surplusPos)
        );
      }
      if (res.type === 'surplusPos') {
        this.surplusPos = this.outsourcingService.deepclone(this.outsourcingService.surplusPos);
        setTimeout(() => {
          this.refreshDeletable();
        }, 0);
      }
      if (res.type === 'surplusLines') {
        this.surplusLines = this.outsourcingService.serialLines(this.outsourcingService.getLines(this.outsourcingService.surplusPos));
      }
      this.cd.detectChanges();
    });
    this.garmentEventEmitter = this.outsourcingService.garmentEventEmitter.subscribe((res) => {
      if (res.onchange === 'lossCountChange') {
        const lossCountList = this.detailForm.get('factorys').controls[res.factoryIndex].get('loss_counts_v2').value ?? [];
        lossCountList[res.poUniqueCode] = res.losscount;

        this.detailForm.get('factorys').controls[res.factoryIndex].get('loss_counts_v2').setValue(lossCountList);
      }
      if (res.onchange === 'dueTimeChange') {
        const dueTimeList = this.detailForm.get('factorys').controls[res.factoryIndex].get('factory_po_due_times').value ?? [];
        dueTimeList[res.poUniqueCode] = res.due_time;

        this.detailForm.get('factorys').controls[res.factoryIndex].get('factory_po_due_times').setValue(dueTimeList);
      }
      // 监听删除po 记录需要删除的 po_line_id
      if (res.onchange === 'deleteDistributions') {
        // 根据数据结构，应该记录 po_line_id 或 order_id
        const deleteId = res.result.po_line_id || res.result.order_id;
        if (deleteId) {
          this.deleteLines.push(deleteId);
        }
      }
      if (res.onchange === 'poChange') {
        this.factoryDataChange = true;
        if (res.mode === 'removePo') {
          this.factoryItem.get(res.factoryIndex)?.setPos(res.result);
        }
        if (res.mode === 'addPo') {
          // 选择交付单， 给交付日期赋初始值
          const tabs = this.factoryItem.get(res.factoryIndex)?.tabs || [];
          const dueTime = this.detailForm.get('factorys').controls[res.factoryIndex]?.get('factory_po_due_times')?.value;
          const selectedPoTabIndex = this.factoryItem.get(res.factoryIndex)!.tabContainer.selectedIndex;
          dueTime[tabs[selectedPoTabIndex]?.po_basic?.po_unique_code] = tabs[selectedPoTabIndex].po_basic?.due_time
            ? new Date(Number(tabs[selectedPoTabIndex].po_basic?.due_time))
            : null;
          this.detailForm.get('factorys').controls[res.factoryIndex]?.get('factory_po_due_times').setValue(dueTime);
        }
        if (res.mode === 'addSurplus') {
          // 添加剩余未分配数据， 给交付日期赋初始值
          const tabs = this.factoryItem.get(res.factoryIndex)?.tabs || [];
          const dueTime = this.detailForm.get('factorys').controls[res.factoryIndex]?.get('factory_po_due_times')?.value || {};
          tabs?.forEach((tab: any) => {
            if (tab.po_basic?.po_unique_code && !dueTime[tab.po_basic.po_unique_code]) {
              dueTime[tab.po_basic.po_unique_code] = tab.po_basic.due_time ? new Date(Number(tab.po_basic.due_time)) : null;
            }
          });
          this.detailForm.get('factorys').controls[res.factoryIndex]?.get('factory_po_due_times').setValue(dueTime);
        }
      }
      // 加工厂变更 将剩余未分配的数据填充进去(当前加工厂 有数据变更时不需要填充数据)
      if (res.onchange === 'factoryChange') {
        // 更新加工厂的校验
        this.detailForm?.get('factorys').controls?.forEach((item: AbstractControl) => {
          item?.get('factory_short_name')?.markAsDirty();
          item?.get('factory_short_name')?.updateValueAndValidity();
        });

        // 验证是否存在同一个加工厂
        // if (this.validatorRepeatFactiory(res, true)) return;

        // 用于下次选择出现重复加工厂恢复数据
        this.factoryItem.get(res.factoryIndex)?.setPreValue(this.detailForm.get('factorys').controls[res.factoryIndex].value);
        if (res.tabIsEmpty) {
          // 注释掉添加剩余未分配（保持UI隐藏状态）
          // this.factoryItem.get(res.factoryIndex)?.handleAddSurplus();

          // 选择加工厂， 给带出来的每个交付单的交付日期赋初始值
          const tabs = this.factoryItem.get(res.factoryIndex)?.tabs || [];
          const dueTime: { [key: string]: any } = {};
          tabs?.forEach((tab: any) => {
            dueTime[tab.po_basic.po_unique_code] = tab.po_basic.due_time ? new Date(Number(tab.po_basic.due_time)) : null;
          });
          this.detailForm.get('factorys').controls[res.factoryIndex]?.get('factory_po_due_times')?.setValue(dueTime);
        }
      }
      /* 展示含税单价参考价 */
      if (res.onchange === 'showInferencePrice') {
        this.nzModalService.create({
          nzTitle: '参考价',
          nzContent: OrderOutsourcingFactoryPriceListComponent,
          nzFooter: null,
          nzClassName: 'flc-confirm-modal',
          nzComponentParams: {
            priceInfo: res.params,
          },
          nzWidth: '40%',
        });
      }
      /* 点击订单完成 */
      if (res.onchange === 'orderFinish') {
        this.flcModalService
          .confirmCancel({
            content: this.translateService.instant('outsourcingMessage.订单完成'),
            showSubContent: true,
            subContent: this.translateService.instant('outsourcingMessage.orderFinishContent'),
            strongConfirm: true,
            iconClass: 'icon-yiwen',
            iconColor: '#4D96FF',
          })
          .afterClose.subscribe((val) => {
            if (val) {
              this._service.orderFinish(res.id).subscribe((res) => {
                if (res.code === 200) {
                  this._message.success(this.translateService.instant('outsourcingMessage.订单完成'));
                  this.getDetail();
                }
              });
            }
          });
      }
      /* 点击一键入库 */
      if (res.onchange === 'oneClickInbound') {
        if (this.orderDetail.status !== OrderStatus.auditPass) {
          this._notifyService.warning('', res?.bulk_code + '大货单未审核，无法一键入库！');
          return;
        }
        this.flcModalService.confirmCancel({ content: '确定一键入库？' }).afterClose.subscribe((result) => {
          if (!result) return;
          this._service.handleOneClickInbound({ bulk_codes: [res?.bulk_code] }).subscribe((res: any) => {
            if (res.code === 200) {
              this._message.success('入库成功');
              // 单条记录生成入库单，自动跳转到入库单详情页；
              if (res?.data?.ids?.length === 1) this._router.navigate(['/product-inventory/product-in-stock/scm/list', res?.data?.ids[0]]);
            }
          });
        });
      }
      if (res.onchange === 'onAdvancePayment') {
        if (this.orderDetail.status !== OrderStatus.auditPass) {
          this._notifyService.warning('', res?.bulk_code + '大货单未审核，无法预付款！');
          return;
        }
        const factory_id = res?.factory_id;
        const { io_uuid } = this.io_basic;
        if (!io_uuid) {
          this._notifyService.warning('', res?.bulk_code + '大货单无法到获取io_uuid！');
          return;
        }
        this._service
          .generateAdvancePayment({
            orders: [
              {
                factory_out_sourcing_id: factory_id,
                io_uuid,
              },
            ],
          })
          .subscribe((_serviceRes: any) => {
            if (_serviceRes.code == 200) {
              const id = _serviceRes?.data?.id ?? 'new';
              this._router.navigate(['/settlement/manufacture-payment-request/list', id]); // 跳转到物料付款详情页
            } else {
              this._message.error('预付款生成失败');
            }
          });
      }
    });

    this._sysSubscription = this._systemSettingService.boardcast.subscribe((result: any) => {
      this.onlyCanSelectOrderOption = result.order?.distributionForceFactory || false;
    });
  }
  // 页面销毁，重置service中的数据
  ngOnDestroy(): void {
    this.outsourcingService.resetData();
    this.garmentEventEmitter?.unsubscribe();
    this.changeValueEmitter?.unsubscribe();
    this._sysSubscription?.unsubscribe();
  }
  // 大货单号自动生成
  autoFillBulkcode(item: any, index: number) {
    let bulkOrderCode = item?.bulk_code;
    if (!bulkOrderCode || bulkOrderCode === '') {
      bulkOrderCode = this.io_basic.io_code + (index + 1).toString().padStart(3, '0');
    }
    return bulkOrderCode;
  }

  // 获取详情回显数据
  getDetail() {
    this.isSpinning = true;
    forkJoin([
      this._service.ioDetail({ id: this.io_id, cache: false, production_type: 1 }),
      this._service.detail({ id: this.order_id, cache: true, production_type: 1, from: 'io_sourcing' }),
      this._service.undistribution({ id: this.order_id, cache: true, production_type: 1 }),
    ])
      .pipe(finalize(() => (this.isSpinning = false)))
      .subscribe(async (res) => {
        if (res[0].code === 200 && res[1].code === 200 && res[2].code === 200) {
          this.outsourcingService.surplusPos = []; //重置数据
          this.outsourcingService.surplusLines = []; //重置数据
          // 给po_lines设置po_id
          const ioDetail = res[0]?.data;

          console.log(
            '🔍 [DEBUG] getDetail - ioDetail.pos 原始数据:',
            ioDetail?.pos?.map((p: any) => ({
              po_id: p.po_basic?.id,
              lines_count: p.po_lines?.length,
              lines_with_qty: p.po_lines?.filter((l: any) => l.qty !== null && l.qty !== undefined).length,
              target_lines: p.po_lines
                ?.filter((l: any) => l.id === 735 || l.id === 736)
                .map((l: any) => ({
                  id: l.id,
                  qty: l.qty,
                  color: l.color_info?.color_name,
                  size: l.size_info?.spec_size,
                })),
            }))
          );

          this.outsourcingService.io_uuid = ioDetail?.io_basic?.io_uuid;
          this.getProdProgress(ioDetail.io_basic.io_uuid);
          res[0]?.data?.pos.forEach((po: any) => {
            po.po_lines.forEach((line: any) => {
              line['po_id'] = po?.po_basic?.id;
            });
          });
          this.orderDetail = res[1]?.data;
          this.io_basic = ioDetail?.io_basic;
          this.io_lines = ioDetail?.io_lines;
          this.pos = ioDetail?.pos;

          console.log(
            '🔍 [DEBUG] getDetail - 设置 this.pos 后:',
            this.pos?.map((p: any) => ({
              po_id: p.po_basic?.id,
              lines_count: p.po_lines?.length,
              lines_with_qty: p.po_lines?.filter((l: any) => l.qty !== null && l.qty !== undefined).length,
              target_lines: p.po_lines
                ?.filter((l: any) => l.id === 735 || l.id === 736)
                .map((l: any) => ({
                  id: l.id,
                  qty: l.qty,
                  color: l.color_info?.color_name,
                  size: l.size_info?.spec_size,
                })),
            }))
          );

          this.outsourcingService.pos = this.outsourcingService.deepclone(ioDetail?.pos);

          console.log(
            '🔍 [DEBUG] getDetail - 设置 outsourcingService.pos 后:',
            this.outsourcingService.pos?.map((p: any) => ({
              po_id: p.po_basic?.id,
              lines_count: p.po_lines?.length,
              lines_with_qty: p.po_lines?.filter((l: any) => l.qty !== null && l.qty !== undefined).length,
              target_lines: p.po_lines
                ?.filter((l: any) => l.id === 735 || l.id === 736)
                .map((l: any) => ({
                  id: l.id,
                  qty: l.qty,
                  color: l.color_info?.color_name,
                  size: l.size_info?.spec_size,
                })),
            }))
          );

          this.outsourcingService.io_lines = this.outsourcingService.deepclone(ioDetail?.io_lines);
          this.orderStatus = this.orderDetail?.status;
          this.detailForm.get('delete_factory_infos')?.setValue([]);
          this.detailForm.get('io_code')?.setValue(this.io_basic.io_code);
          this.detailForm.get('io_id_local')?.setValue(this.io_id);
          this.detailForm.get('io_id')?.setValue(this.io_id);
          this.detailForm.get('style_code').setValue(this.io_basic.style_code);
          this.detailForm.get('style_code_uuid').setValue(this.io_basic.style_code_uuid);
          this.ioDefaultValue = { label: this.detailForm.get('io_code')?.value, value: this.detailForm.get('io_id_local')?.value };
          this.extraOptions = [
            { label: this.detailForm.get('io_code')?.value, value: this.detailForm.get('io_id_local')?.value, hide: false },
          ];
          // 获取大货单所有的pos下的line_id
          const allIoPosLineIds: any = [];
          ioDetail?.pos.forEach((po: any) => {
            po.po_lines.forEach((line: Line) => {
              allIoPosLineIds.push(line.id);
            });
          });
          this.deleteLines = [];

          this.detailForm.get('factorys').clear();
          this.orderDetail?.info.forEach((item: any, index: number) => {
            // 循环当前加工厂完整的line,在所有的po下line_ids集合中找不到 将其放在需要删除的line_ids中
            item.lines.forEach((line: any) => {
              line.factory_id = item.id;
              if (!allIoPosLineIds.includes(line.po_line_id)) {
                this.deleteLines.push(line.po_line_id);
              }
            });

            console.log('🔍 [DEBUG] 调用 getEnabledLines 前 - item.lines:', item.lines);
            console.log(
              '🔍 [DEBUG] 调用 getEnabledLines 前 - this.pos 中的目标 lines:',
              this.pos?.map((p: any) => ({
                po_id: p.po_basic?.id,
                target_lines: p.po_lines
                  ?.filter((l: any) => l.id === 735 || l.id === 736)
                  .map((l: any) => ({
                    id: l.id,
                    qty: l.qty,
                    color: l.color_info?.color_name,
                    size: l.size_info?.spec_size,
                  })),
              }))
            );

            const { pos, po_lines } = this.outsourcingService.getEnabledLines(item.lines, this.pos);

            console.log(
              '🔍 [DEBUG] getEnabledLines 返回后 - pos 中的目标 lines:',
              pos?.map((p: any) => ({
                po_id: p.po_basic?.id,
                target_lines: p.po_lines
                  ?.filter((l: any) => l.id === 735 || l.id === 736)
                  .map((l: any) => ({
                    id: l.id,
                    qty: l.qty,
                    color: l.color_info?.color_name,
                    size: l.size_info?.spec_size,
                  })),
              }))
            );

            pos.forEach((po: Po) => {
              po.po_lines.forEach((line: Line) => {
                if (line.qty === 0) {
                  line._qty = line.qty;
                  line.qty = null;
                }
                this.orderDetail.info.forEach((e: any) => {
                  e.lines.forEach((l: any) => {
                    if (l.po_id === line.po_id && l.po_line_id === line.id && e.id === line.factory_id) {
                      const oldQty = line.qty;
                      line.qty = l.qty;

                      if (line.id === 735 || line.id === 736) {
                        console.log(`🔍 [DEBUG] ❌ 第二次覆盖 line ${line.id} qty: ${oldQty} -> ${l.qty}`);
                        console.log(
                          `🔍 [DEBUG] 覆盖来源 - e.id: ${e.id}, l.po_id: ${l.po_id}, l.po_line_id: ${l.po_line_id}, l.qty: ${l.qty}`
                        );
                      }
                    }
                  });
                });
                line.deletable = line.order_id ? line.deletable : true;
                // 如果已完成，都不能改
                line.extraParams = {
                  cellEditable: item.finish ? false : line.order_id ? line.deletable : true,
                };
              });
            });

            const lossCounts: { [key: string]: any } = {};
            (item?.loss_counts_v2 ?? []).forEach((item: any) => {
              lossCounts[item.po_unique_code] = item.loss_count_v2;
            });
            // 交付日期
            const dueTime: { [key: string]: any } = {};
            if (!item?.factory_po_due_times?.length) {
              // 赋值订单需求的交付单的交付日期
              pos?.forEach((po: any) => {
                dueTime[po.po_basic?.po_unique_code] = po?.po_basic?.due_time ? new Date(Number(po?.po_basic?.due_time)) : null;
              });
            } else {
              (item?.factory_po_due_times ?? []).forEach((item: any) => {
                dueTime[item.po_unique_code] = new Date(item.po_due_time);
              });
            }

            const bulkOrderCode = this.autoFillBulkcode(item, index);
            const stage_ids = item?.stage_list?.map((s: any) => {
              return s.stage_id;
            });
            const factory = this.fb.group({
              deletable: [item?.deletable],
              factory_id: [item?.id],
              bulk_code: [bulkOrderCode, [Validators.required]],
              factory_short_name: [item?.factory_short_name, [this.repeatFactoryValidator()]],
              factory_code: [item?.factory_code, []],
              factory_name: [item?.factory_name],
              factory_archive_code: [item?.factory_archive_code],
              factory_archive_id: [item?.factory_archive_id],
              lines: [po_lines],
              unit_price: [item?.unit_price, [Validators.required, this._validator.greaterThanValidator(0)]],
              tax_rate: [item?.tax_rate, [Validators.required, this._validator.greaterThanValidator(-1)]],
              tax_price: [item?.tax_price, [Validators.required, this._validator.greaterThanValidator(0)]],
              finish: [item?.finish],
              check_type: [item?.check_type, [Validators.required]],
              merchandiser_id: [{ value: item?.merchandiser_id ?? null, label: item?.merchandiser_name ?? null }, {}],
              merchandiser_name: [item?.merchandiser_name],
              check_radio: [item?.check_radio === '0' ? null : item?.check_radio],
              qc: [item?.qc],
              qc_id: [{ label: item?.qc ?? null, value: item?.qc_id ?? null }, {}],
              loss_counts_v2: [lossCounts, {}],
              line_no: [item?.line_no],
              line_name: [item?.line_name],
              plan_time: [item?.plan_start_time ? [item.plan_start_time, item.plan_end_time] : []], // 生产计划时间
              remark: [item?.remark],
              check_has_started: [item?.check_has_started],

              stage_ids: [stage_ids, [Validators.required]], // 工段 默认全选
              stage_list: [item?.stage_list],
              part_names: [item?.part_names], // 部位
              advance_payment_ratio: [item?.advance_payment_ratio], //  预付款比例
              advance_payment_money: [item?.advance_payment_money], //  预付款金额
              payment_method: [item?.payment_method], //  付款方式
              payment_method_id: [item?.payment_method_id ? item?.payment_method_id.toString() : null], //  付款方式id
              settlement_method: [item?.settlement_method], //  结算方式
              settlement_method_id: [item?.settlement_method_id ? item?.settlement_method_id?.toString() : null], //  结算方式id
              factory_po_due_times: [dueTime, {}],
            });

            const _option = this.factory_options.find((_item) => _item.label === item.factory_short_name);
            // 有产线的工厂，产线必填
            if (_option?.order_distribute_type === 2) {
              factory.get('line_no')?.addValidators(Validators.required);
            }
            /* 已接单含税单价不能修改 */
            /* 6-10华洋版本含税单价一直可修改 */
            if (!item?.deletable) {
              // factory.get('unit_price')?.disable();
              // factory.get('tax_rate')?.disable();
              factory.get('tax_price')?.disable();
            }
            this.detailForm.get('factorys').push(factory);
            setTimeout(async () => {
              console.log('🔍 [DEBUG] 调用 setPos 前 - pos 数据:', pos?.map((p: any) => ({
                po_id: p.po_basic?.id,
                lines_count: p.po_lines?.length,
                lines_with_qty: p.po_lines?.filter((l: any) => l.qty !== null && l.qty !== undefined).length,
                target_lines: p.po_lines?.filter((l: any) => l.id === 735 || l.id === 736).map((l: any) => ({
                  id: l.id,
                  qty: l.qty,
                  color: l.color_info?.color_name,
                  size: l.size_info?.spec_size
                }))
              })));

              const serializedPos = this.outsourcingService.serialPos(this.outsourcingService.deepclone(pos));
              console.log('🔍 [DEBUG] serialPos 处理后:', serializedPos?.map((p: any) => ({
                po_id: p.po_basic?.id,
                lines_count: p.po_lines?.length,
                lines_with_qty: p.po_lines?.filter((l: any) => l.qty !== null && l.qty !== undefined).length,
                target_lines: p.po_lines?.filter((l: any) => l.id === 735 || l.id === 736).map((l: any) => ({
                  id: l.id,
                  qty: l.qty,
                  color: l.color_info?.color_name,
                  size: l.size_info?.spec_size
                }))
              })));

              this.factoryItem.get(index)?.setPos(serializedPos);
              this.factoryItem.get(index)?.setPoLines(this.outsourcingService.serialLines(this.outsourcingService.getLines(pos)));
              this.factoryItem.get(index)?.setSurplusPos(this.outsourcingService.surplusPos);
              if (this.orderStatus === OrderStatus.toModifyAudit) {
                await this.getFactoryChangedPo();
              }
            }, 0);
          });
          if (!this.orderDetail?.info?.length) {
            const factory = this.fb.group({
              deletable: [true],
              factory_id: [null],
              bulk_code: [this.autoFillBulkcode(null, 0), [Validators.required]],
              factory_short_name: [null, [this.repeatFactoryValidator()]],
              factory_code: [null, []],
              factory_name: [null],
              factory_archive_code: [null],
              factory_archive_id: [null],
              lines: [null],
              unit_price: [null, [Validators.required, this._validator.greaterThanValidator(0)]],
              tax_rate: ['0', [Validators.required, this._validator.greaterThanValidator(-1)]],
              tax_price: [null, [Validators.required, this._validator.greaterThanValidator(0)]],
              finish: [null],
              check_type: [CheckTypeEnum.partCheck, [Validators.required]], // 默认选中抽检
              merchandiser_id: [null],
              merchandiser_name: [null],
              check_radio: [null],
              qc: [null],
              qc_id: [null],
              loss_counts_v2: [null ?? {}],
              line_no: [null],
              line_name: [null],
              pplan_time: [null],
              remark: [null],
              check_has_started: [null],

              stage_ids: [null, [Validators.required]], // 工段 默认全选
              stage_list: [[]],
              part_names: [null], // 部位
              advance_payment_ratio: [null], //  预付款比例
              advance_payment_money: [null], //  预付款金额
              payment_method: [null], //  付款方式
              payment_method_id: [null], //  付款方式id
              settlement_method: [null], //  结算方式
              settlement_method_id: [null], //  结算方式id
              factory_po_due_times: [null, {}], // 交付日期
            });
            this.detailForm.get('factorys').push(factory);
          }

          // 回显剩余未分配
          const surplusPos: Po[] = [];
          const surplusLines: any[] = [];
          this.pos.forEach((e: any) => {
            const data = this.outsourcingService.deepclone(e);
            data.po_lines.forEach((po: any) => {
              this.orderDetail.info.forEach((assaginFactory: any) => {
                assaginFactory.lines.forEach((assaginPo: any) => {
                  if (assaginPo.po_line_id === po.id) {
                    po.qty = po.qty - assaginPo.qty;
                    po.over = po.qty < 0;
                  }
                });
              });
              surplusLines.push(this.outsourcingService.deepclone(po));
            });
            surplusPos.push(data);
          });
          this.outsourcingService.surplusPos.push(...this.outsourcingService.serialNumberLines(surplusPos));
          this.outsourcingService.surplusLines.push(...surplusLines);
          this.surplusPos = this.outsourcingService.deepclone(this.outsourcingService.surplusPos);
          this.surplusLines = this.outsourcingService.serialLines(this.outsourcingService.getLines(this.outsourcingService.surplusPos));
        }
      });
  }
  // 操作line po 加工厂时 刷新每个加工厂内下po下line.deletable的值
  // po_line_ids 快速查找 不传则刷新line_id含有的数据大于等于2条的
  refreshDeletable() {
    const surplusMap = new Map();
    // 记录剩余未分配的po_line_id数据
    this.outsourcingService.surplusPos.forEach((po: Po) => {
      po.po_lines.forEach((line) => {
        surplusMap.set(line.id, line);
      });
    });
    // 循环每个加工厂
    this.factoryItem.toArray().forEach((factory: OrderOutsourcingFactoryItemComponent, index: number) => {
      const factoryPosMap = new Map();
      // 循环加工厂的 pos数据 po_line_id
      factory._pos.forEach((po: Po) => {
        po.po_lines.forEach((line: Line) => {
          // 查询剩余剩余未分配中是否含有当前po_line_id。
          // 若剩余未分配中含有则 当前的deletable  = true
          // 若剩余未分配中不存在，当前qty为null ，则deleteable = false
          const po_line: Line = surplusMap.get(line.id);
          // 处理新增加工厂 添加剩余未分配的数据时同步当前po 所有数据没有extraParams问题
          if (!line?.extraParams) {
            line.extraParams = { cellEditable: false };
          }
          if (po_line === undefined && line.qty === null) {
            line.extraParams!.cellEditable = true;
          } else if (line.deletable || (po_line && !line.deletable)) {
            // (po_line && !line.deletable) 处理 审核通过 接单的外发单，大货中新增了颜色尺码 需要更正
            line.extraParams!.cellEditable = true;
          }
          // 如果已完成，都不能改
          if (factory.parentGroup.get('finish')?.value) {
            line.extraParams!.cellEditable = false;
          }
          factoryPosMap.set(line.id, line);
        });
      });
      factory?._pos.forEach((po: Po, poIndex: number) => {
        this.outsourcingService.changePoLineEmitter.emit({ isPoChange: true, factoryIndex: index, poIndex, poLines: [...po.po_lines] });
      });
    });
  }
  getFactoryChangedPo() {
    return new Promise((resolve) => {
      this._service.getFactoryChangedPo({ id: Number(this.order_id) }).subscribe((res) => {
        if (res?.code === 200) {
          const factoryMap = new Map();
          res.data?.data?.forEach((item: any) => {
            if (factoryMap?.get(item.factory_code)) {
              factoryMap.set(item.factory_code, [...factoryMap.get(item.factory_code), ...item.changed_pos]);
            } else {
              factoryMap.set(item.factory_code, [...item.changed_pos]);
            }
          });
          this.factoryItem.toArray().forEach((factory) => {
            const _factory = factoryMap.get(factory.preFactoryValue.factory_code);
            factory._pos.forEach((po: Po) => {
              if (_factory.includes(po.po_basic.id)) {
                po.po_basic.have_changed_line = true;
              }
            });
            this.pos = this.outsourcingService.deepclone(this.pos);
          });
          resolve(true);
        }
      });
    });
  }
  // 提交
  commit(isCommit = true) {
    // 注释掉剩余未分配的颜色尺码件数超出检查，使用新的工段分配逻辑
    // let overCount = false;
    // this.surplusPos.forEach((po: Po) => {
    //   po.po_lines.forEach((line: Line) => {
    //     if (line.qty < 0) {
    //       overCount = true;
    //       return;
    //     }
    //   });
    // });
    // if (overCount) {
    //   this._message.error(this.translateKey('outsourcingMessage.存在颜色尺码超出可分配件数'));
    //   return;
    // }

    // 1、修改超出可分配件数逻辑：
    // 查看所有加工厂是否有相同工段，如果没有，则加工厂的最大分配件数为订单数量
    // 如果某些加工厂具有相同的工段，则这些工厂的最大分配件数加起来为订单数量
    // 需要按每个颜色进行精确判断
    const factorys = this.detailForm.get('factorys').value;

    console.log('factorys:', factorys);
    console.log('io_lines:', this.io_lines);

    // 构建订单颜色+尺码数量映射 {color_id_size_id: {color_name: string, size_name: string, total_qty: number}}
    const orderColorSizeMap = new Map<string, { color_name: string; size_name: string; total_qty: number }>();
    this.io_lines?.forEach((line: any) => {
      const colorId = line.color_info?.color_id || line.color_id;
      const colorName = line.color_info?.color_name || line.color_name || '未知颜色';
      const sizeId = line.size_info?.spec_id || line.size_id;
      const sizeName = line.size_info?.spec_size || line.size_name || '未知尺码';
      const qty = Number(line?.qty ?? 0);

      const colorSizeKey = `${colorId}_${sizeId}`;

      if (orderColorSizeMap.has(colorSizeKey)) {
        const existing = orderColorSizeMap.get(colorSizeKey)!;
        existing.total_qty = this.flcUtilService.accAdd(existing.total_qty, qty);
      } else {
        orderColorSizeMap.set(colorSizeKey, {
          color_name: colorName,
          size_name: sizeName,
          total_qty: qty,
        });
      }
    });

    // 按工段和颜色+尺码分组加工厂的分配数量
    const stageColorSizeFactoryMap = new Map<string, Map<string, any[]>>(); // key: stage_name, value: Map<color_id_size_id, factory[]>

    factorys.forEach((factory: any, factoryIndex: number) => {
      if (factory?.stage_list?.length && factory?.lines?.length) {
        factory?.stage_list?.forEach((stage: any) => {
          if (!stageColorSizeFactoryMap.has(stage?.stage_name)) {
            stageColorSizeFactoryMap.set(stage?.stage_name, new Map());
          }
          const stageMap = stageColorSizeFactoryMap.get(stage?.stage_name)!;

          // 按颜色+尺码统计该工厂在该工段的分配数量
          const factoryColorSizeMap = new Map<string, { qty: number; colorName: string; sizeName: string }>();
          factory.lines.forEach((line: any) => {
            const colorId = line.color_info?.color_id || line.color_id;
            const colorName = line.color_info?.color_name || line.color_name || '未知颜色';
            const sizeId = line.size_info?.spec_id || line.size_id;
            const sizeName = line.size_info?.spec_size || line.size_name || '未知尺码';
            const qty = Number(line?.qty ?? 0);

            const colorSizeKey = `${colorId}_${sizeId}`;

            if (factoryColorSizeMap.has(colorSizeKey)) {
              const existing = factoryColorSizeMap.get(colorSizeKey)!;
              existing.qty = this.flcUtilService.accAdd(existing.qty, qty);
            } else {
              factoryColorSizeMap.set(colorSizeKey, { qty, colorName, sizeName });
            }
          });

          // 为每个颜色+尺码添加工厂信息
          factoryColorSizeMap.forEach((colorSizeData, colorSizeKey) => {
            if (!stageMap.has(colorSizeKey)) {
              stageMap.set(colorSizeKey, []);
            }
            stageMap.get(colorSizeKey)!.push({
              ...factory,
              factoryIndex,
              colorSizeQty: colorSizeData.qty,
              colorName: colorSizeData.colorName,
              sizeName: colorSizeData.sizeName,
            });
          });
        });
      }
    });

    // 检查每个工段每个颜色+尺码的分配逻辑，按工段分组显示错误信息
    const overStages = new Set<string>(); // 收集超出分配的工段名称

    stageColorSizeFactoryMap.forEach((colorSizeMap, stage_name) => {
      let stageHasError = false; // 标记当前工段是否有超出分配的情况

      colorSizeMap.forEach((factories, colorSizeKey) => {
        const orderColorSizeInfo = orderColorSizeMap.get(colorSizeKey);
        if (!orderColorSizeInfo) {
          return;
        }

        const orderQty = orderColorSizeInfo.total_qty;

        if (factories.length === 1) {
          // 如果只有一个加工厂有这个工段的这个颜色+尺码，则该加工厂的最大分配件数为该颜色+尺码的订单数量
          const factory = factories[0];
          const factoryQty = Number(factory.colorSizeQty ?? 0);

          if (factoryQty > Number(orderQty ?? 0)) {
            stageHasError = true;
          }
        } else {
          // 如果多个加工厂有相同工段的相同颜色+尺码，则这些加工厂的分配件数总和不能超过该颜色+尺码的订单数量

          const totalColorSizeQty = factories.reduce((prev: number, factory: any) => {
            return this.flcUtilService.accAdd(prev, Number(factory.colorSizeQty ?? 0));
          }, 0);

          if (Number(totalColorSizeQty ?? 0) > Number(orderQty ?? 0)) {
            stageHasError = true;
          }
        }
      });

      // 如果当前工段有超出分配的情况，添加到错误工段集合中
      if (stageHasError) {
        overStages.add(stage_name);
      }
    });

    if (overStages.size > 0) {
      const stageList = Array.from(overStages).join('、');
      this._notifyService.error(`【${stageList}】工段外发件数超过订单件数！`, '');
      return;
    }

    // 是提交添加加工厂类型校验
    if (isCommit) {
      if (this._validator.formIsInvalid(this.detailForm)) {
        this._message.error('请检查必填项');
        return;
      }
    } else {
      // 是保存 去除原有的验证器
      if (!this.detailForm.get('io_id')?.valid) {
        this.detailForm.get('io_id')?.markAsDirty();
        this.detailForm.get('io_id')?.updateValueAndValidity();
        return;
      }
    }
    if (isCommit) {
      // 空的加工厂集合
      // 空的加工厂集合与加工厂个数相等，则提示至少分配一个加工厂
      const factorys = this.detailForm.get('factorys').value;
      const emptFactoryName = [];
      for (let i = 0; i < factorys.length; i++) {
        const factory = factorys[i];
        if (!factory.factory_short_name) {
          emptFactoryName.push(null);
        }
      }
      if (factorys.length === emptFactoryName.length) {
        this._message.error(this.translateKey('outsourcingMessage.请至少分配一个加工厂'));
        return;
      }
      for (let i = 0; i < factorys.length; i++) {
        const factory = factorys[i];
        // 验证相同加工厂不能有多个含税单价
        const _unit_prices = factorys
          .filter((item: any) => item.factory_short_name && item.factory_short_name === factory.factory_short_name)
          .map((item: any) => (item.unit_price ? parseFloat(item.unit_price) : 0))
          .filter((item: any) => item);
        const _tax_rates = factorys
          .filter((item: any) => item.factory_short_name && item.factory_short_name === factory.factory_short_name)
          .map((item: any) => (item.unit_price ? parseFloat(item.unit_price) : 0))
          .filter((item: any) => item);
        if (new Set(_unit_prices).size > 1 || new Set(_tax_rates).size > 1) {
          this._message.error('外发同一个工厂，单价和税率需一致');
          return;
        }

        // 如果设置了预计外发厂，且配置了只能选预计外发厂则校验是否是预计外发厂
        if (
          this.onlyCanSelectOrderOption &&
          this.io_basic?.process_factory_name &&
          factory.factory_short_name &&
          factory.factory_short_name !== this.io_basic?.process_factory_name
        ) {
          this._message.error(`该订单只能分配给${this.io_basic?.process_factory_name}加工厂`);
          return;
        }

        const pos = this.factoryItem.get(i)!._pos;
        const parentGroupValue = this.factoryItem.get(i)!.parentGroup?.getRawValue();
        // 加工厂下的_pos没有数据 报错
        if (!pos.length && factory.factory_short_name) {
          this.resetFactoryToggle(i);
          this._message.error(
            `${'【' + factory.factory_short_name + '】' + this.translateKey('outsourcingMessage.加工厂下交付单不能为空')}`
          );
          return;
        } else {
          if (!factory.factory_short_name) {
            this.resetFactoryToggle(i);
            this._message.error(
              this.translateKey('outsourcingMessage.第') + (i + 1) + this.translateKey('outsourcingMessage.个加工厂未选择')
            );
            return;
          }
          for (let index = 0; index < pos.length; index++) {
            if (!pos[index]?.po_basic?.po_code) {
              this.resetFactoryToggle(i);
              this._message.error(`【${factory.factory_short_name}】${this.translateKey('outsourcingMessage.加工厂下交付单不能为空')}`);
              this.factoryItem.get(i)!.tabContainer.selectedIndex = index;
              return;
            }
            // 判断交付日期是否为空
            const po_unique_code = pos[index]?.po_basic?.po_unique_code; // 当前po的唯一标识
            if (!parentGroupValue?.factory_po_due_times?.[po_unique_code]) {
              this.resetFactoryToggle(i);
              this._message.error(
                `【${factory.factory_short_name}】${this.translateKey('outsourcingMessage.加工厂下')}【${
                  pos[index]?.po_basic?.po_code
                }】${this.translateKey('outsourcingMessage.交付单的交付日期不能为空')}`
              );
              this.factoryItem.get(i)!.tabContainer.selectedIndex = index;
              return;
            }
            if (!pos[index].po_lines.filter((item: Line) => item.qty).length) {
              this.resetFactoryToggle(i);
              this._message.error(
                `【${factory.factory_short_name}】${this.translateKey('outsourcingMessage.加工厂下')}【${
                  pos[index]?.po_basic?.po_code
                }】${this.translateKey('outsourcingMessage.交付单颜色/尺码不能为空')}`
              );
              this.factoryItem.get(i)!.tabContainer.selectedIndex = index;
              return;
            }
          }
        }
      }
    }
    // 是否还有剩余未分配的颜色尺码，有且是提交 则二次弹框确认
    if (this.outsourcingService.surplusPos.length && isCommit) {
      this.outsourcingService
        .confirmDialog(this.translateKey('outsourcingMessage.有交付单未分配完毕，确定提交？'), ' ', false, true)
        .afterClose.subscribe((confirm) => {
          if (confirm) {
            this.confirmCommit(isCommit);
          }
        });
    } else {
      this.confirmCommit(isCommit);
    }
  }
  // 重置加工厂的展开收起状态，符合调整的展开 否则收起
  resetFactoryToggle(i: number): void {
    this.factoryItem.toArray().forEach((factoryItem: any, factoryItemIndex: number) => {
      if (i !== factoryItemIndex) {
        factoryItem.setToggle(false);
      }
      if (i === factoryItemIndex) {
        factoryItem.setToggle(true);
      }
    });
  }
  // 确定提交
  confirmCommit(isCommit = true) {
    const info: any = [];
    this.detailForm.get('factorys').controls.forEach((item: any, index: number) => {
      if (item.get('factory_name')?.value) {
        const lossCounts: any[] = [];
        Object.entries(item.get('loss_counts_v2').value ?? {}).forEach((item) => {
          if (isNotNil(item[1])) {
            lossCounts.push({
              po_unique_code: item[0],
              loss_count_v2: item[1],
            });
          }
        });
        const factory_po_due_times: any[] = [];
        // 获取当前加工厂实际拥有的交付单
        const currentFactoryPos = this.factoryItem.get(index)?._pos || [];
        const currentFactoryPoUniqueCodes = currentFactoryPos.map((po: any) => po.po_basic?.po_unique_code).filter(Boolean);

        Object.entries(item.get('factory_po_due_times').value ?? {}).forEach((item: any) => {
          if (isNotNil(item[1]) && currentFactoryPoUniqueCodes.includes(item[0])) {
            factory_po_due_times.push({
              po_unique_code: item[0],
              po_due_time: format(startOfDay(item[1]), 'T'),
            });
          }
        });
        const plan_time = item.get('plan_time')?.value;
        info.push({
          id: item.get('factory_id')?.value || 0, // 分配单id，
          bulk_code: item.get('bulk_code')?.value, // bulk_code
          factory_code: item.get('factory_code')?.value, // 分配的工厂code
          factory_name: item.get('factory_name')?.value, // 分配的工厂名成
          factory_short_name: item.get('factory_short_name')?.value, // 分配工厂的简称
          factory_archive_code: item.get('factory_archive_code')?.value,
          factory_archive_id: item.get('factory_archive_id')?.value,
          lines: this.filterPayloadLines(item.get('lines')?.value, this.io_id === 'add', item.get('factory_id')?.value || 0),
          deletable: item.get('deletable')?.value, // 分配单是否可删除，
          unit_price: this.flcUtilService.isNilOrEmptyStr(item.get('unit_price')?.value) ? null : `${item.get('unit_price')?.value}`, //  单价
          tax_rate: this.flcUtilService.isNilOrEmptyStr(item.get('tax_rate')?.value) ? null : `${item.get('tax_rate')?.value}`, // 税率
          tax_price: this.flcUtilService.isNilOrEmptyStr(item.get('tax_price')?.value) ? null : `${item.get('tax_price')?.value}`, // 含税单价
          check_type: item.get('check_type')?.value, // 检验类型
          merchandiser_id: item.get('merchandiser_id')?.value?.value,
          merchandiser_name: item.get('merchandiser_id')?.value?.label,
          qc_id: item.get('qc_id')?.value?.value,
          qc: item.get('qc_id')?.value?.label,
          loss_counts_v2: lossCounts ?? [],
          line_no: item.get('line_no')?.value,
          line_name: item.get('line_name')?.value,
          plan_start_time: plan_time?.length ? format(plan_time[0], 'T') : null,
          plan_end_time: plan_time?.length ? format(plan_time[1], 'T') : null,
          remark: item.get('remark')?.value,
          check_radio: this.flcUtilService.isNilOrEmptyStr(item.get('check_radio')?.value) ? null : `${item.get('check_radio')?.value}`, // 抽检比例
          extra_process_name: '', // 二次工艺名称，成衣外发填写默认值即可
          extra_process_id: 0, // 二次工艺id，成衣外发填写默认值即可

          stage_ids: item?.get('stage_ids')?.value, // 工段 默认全选
          stage_list: item?.get('stage_list')?.value,
          part_names: item.get('part_names')?.value, // 部位
          advance_payment_ratio: this.flcUtilService.isNilOrEmptyStr(item.get('advance_payment_ratio')?.value)
            ? null
            : `${item.get('advance_payment_ratio')?.value}`, //  预付款比例
          advance_payment_money: this.flcUtilService.isNilOrEmptyStr(item.get('advance_payment_money')?.value)
            ? null
            : `${item.get('advance_payment_money')?.value}`, //  预付款金额
          payment_method: item.get('payment_method')?.value, //  付款方式
          payment_method_id: item.get('payment_method_id')?.value, //  付款方式
          settlement_method: item.get('settlement_method')?.value, //  结算方式
          settlement_method_id: item.get('settlement_method_id')?.value, //  结算方式
          factory_po_due_times: factory_po_due_times ?? [],
        });
      }
    });
    const payload: any = {
      commit: isCommit, // 是否提交
      id: Number(this.order_id) || 0,
      production_type: 1, // 外发类型，1 成衣加工 2 二次工艺加工
      extra_process_info: [],
      info: info,
      io_id: Number(this.detailForm.get('io_id').value),
      status: 0, // 分配单状态
      status_value: '', // 分配单状态值
      reason: '', // 退回修改原因
    };

    // 更新数据
    if (this.io_id !== 'add') {
      const delete_info: any = {
        delete_factory_infos: this.detailForm.get('delete_factory_infos').value.filter((value: number) => value), // 删除加工厂的id
        delete_distributions: [], // 删除交付单 单个尺码的数据时
      };
      // 统计每个加工厂需要删除的id
      this.detailForm.get('factorys').value.forEach((item: any, index: number) => {
        this.factoryItem.get(index)?._pos.forEach((po: Po) => {
          po.po_lines.forEach((line: Line) => {
            // 加工厂一致，但qty为null,切初始化_qty不为0(初始为0 qty会转为null) 删除
            if (line.qty === null && item.factory_id === line.factory_id && line?._qty !== 0) {
              delete_info.delete_distributions.push(line.order_id);
            }
            // 加工厂不一致 且qty有值 删除
            if (item.factory_id !== line.factory_id && line.factory_id && line.qty) {
              delete_info.delete_distributions.push(line.order_id);
            }
          });
        });
      });
      // 统计剩余未分配中需要删除的id
      this.surplusPos.forEach((po: Po) => {
        po.po_lines.forEach((line: Line) => {
          if (line.factory_id) {
            delete_info.delete_distributions.push(line.order_id);
          }
        });
      });
      // 用户切换了大货单号，且不是新建模式下 需要原有的加工厂id,po_lines_id
      if (this.isChangeIoId === true && this.order_id !== 'add') {
        this.orderDetail.info.forEach((item: any) => {
          delete_info.delete_factory_infos.push(item.id);
        });
      }
      // 清理 deleteLines，只保留当前表单中不存在的 po_line_id 或 order_id
      const currentIds = new Set<number>();
      this.detailForm.get('factorys').value.forEach((_item: any, index: number) => {
        this.factoryItem.get(index)?._pos.forEach((po: Po) => {
          po.po_lines.forEach((line: Line) => {
            if (line.po_line_id) {
              currentIds.add(line.po_line_id);
            }
            if (line.order_id) {
              currentIds.add(line.order_id);
            }
          });
        });
      });

      // 只保留当前表单中不存在的 deleteLines
      const validDeleteLines = this.deleteLines.filter((id) => !currentIds.has(id));

      delete_info.delete_distributions = [...new Set(delete_info.delete_distributions), ...validDeleteLines];
      // 审核通过走edit编辑接口,其他状态更新走update接口
      const name = [OrderStatus.auditPass, OrderStatus.modifyAuditReturn, OrderStatus.toModifyAudit].includes(this.orderDetail.status)
        ? 'auditPassEdit'
        : 'update';
      this._service[name]({ distribution_order: payload, delete_info }).subscribe((res) => {
        if (res.code === 200) {
          // 提交成功后清空删除记录
          this.deleteLines = [];
          this.detailForm.reset();
          this.editMode = 'read';
          this.io_id = payload.io_id;
          this._router.navigate(['/outsourcing-manage/garment-outsourcing/list/', this.order_id], {
            queryParams: { io_id: payload.io_id },
          });
          this.getDetail();
          this._message.success(this.translateKey('success.update'));
          this._service.eventEmitter.emit('refresh');
        }
      });
    } else {
      // 创建数据
      this._service.create(payload).subscribe((res) => {
        if (res.code === 200) {
          // 创建成功后清空删除记录
          this.deleteLines = [];
          this.io_id = this.detailForm.get('io_id')?.value;
          this.order_id = res.data.id;
          this.editMode = 'read';
          this.detailForm.reset();
          this.getDetail();
          this.initEditorInfoDef();
          this._message.success(this.translateKey('success.create'));
          this._service.eventEmitter.emit('refresh');
          this._router.navigate(['/outsourcing-manage/garment-outsourcing/list/', this.order_id], { queryParams: { io_id: this.io_id } });
        }
      });
    }
  }
  // 处理lines 返回成后端需要的数据格式体
  /**
   *
   * @param lines
   * @param isCreate 是否是创建 id为0 不是创建取老数据
   * @returns
   */
  filterPayloadLines(
    lines: Array<Line>,
    isCreate: boolean,
    factory_id: number
  ): Array<{ id: number; po_id?: number; po_line_id?: number; deletable: boolean; qty: number }> {
    const result: Array<{ id: number; po_id?: number; po_line_id?: number; deletable: boolean; qty: number }> = [];
    lines.forEach((line: Line) => {
      result.push({
        id: isCreate ? 0 : line.factory_id === factory_id ? line.order_id! : 0,
        po_id: line.po_id,
        po_line_id: line?.id,
        deletable: line.deletable,
        qty: line.qty,
      });
    });
    return [...result];
  }
  factoryDataChange = false;
  // 取消
  cancel() {
    if ((this.detailForm.dirty || this.factoryDataChange) && this.editMode !== 'add') {
      this.outsourcingService
        .confirmDialog(this.translateKey('outsourcingMessage.确定取消当前操作？'), ' ', false, true)
        .afterClose.subscribe((confirm) => {
          if (confirm) {
            this.detailForm.markAsPristine();
            this.factoryDataChange = false;
            this.editMode = 'read';
            this.getDetail();
          }
        });
    } else {
      if (this.io_id === 'add') {
        this.back();
      } else {
        this.editMode = 'read';
        this.getDetail();
      }
    }
  }
  // 退回修改
  modify() {
    this.outsourcingService.confirmDialogWithReason().afterClose.subscribe((result) => {
      if (result?.success) {
        this._service.modify({ id: this.order_id, reason: result.reason }).subscribe((res) => {
          if (res.code === 200) {
            this._message.success(this.translateKey('outsourcingMessage.退回修改成功'));
            this.getDetail();
            this._service.eventEmitter.emit('refresh');
          }
        });
      }
    });
  }
  // 审核通过
  pass() {
    for (let i = 0; i < this.detailForm.get('factorys').value.length; i++) {
      const factory = this.detailForm.get('factorys').value[i];
      const pos = this.factoryItem.get(i)!._pos;
      // 加工厂下的_pos没有数据 报错
      if (!pos.length && factory.factory_short_name) {
        this._message.error(
          `${'【' + factory.factory_short_name + '】' + this.translateKey('outsourcingMessage.加工厂下交付单不能为空')}${this.translateKey(
            'outsourcingMessage.请退回修改'
          )}`
        );
        return;
      } else {
        for (let index = 0; index < pos.length; index++) {
          if (!pos[index].po_lines.filter((item: Line) => item.qty).length) {
            this._message.error(
              `【${factory.factory_short_name}】${this.translateKey('outsourcingMessage.加工厂下')}【${
                pos[index]?.po_basic?.po_code
              }】${this.translateKey('outsourcingMessage.交付单颜色/尺码不能为空')}${this.translateKey('outsourcingMessage.请退回修改')}`
            );
            return;
          }
        }
      }
    }
    this.outsourcingService
      .confirmDialog(
        this.translateKey('outsourcingMessage.确定审核通过？'),
        this.translateKey('outsourcingMessage.审核通过后订单状态变为待外发厂接单'),
        true,
        false
      )
      .afterClose.subscribe((confirm) => {
        if (confirm) {
          this._service.pass(this.order_id).subscribe((res) => {
            if (res.code === 200) {
              this._message.success(this.translateKey('success.pass'));
              this._service.batchCreateProductionTable({ io_out_sourcing_ids: [this.order_id], operation_type: 1 }).subscribe((res) => {});
              this._service.createMaterialShipmentPlan([this.io_basic?.io_uuid]).subscribe((res) => {});
              this.getDetail();
              this._service.eventEmitter.emit('refresh');
              // 自动创建打样任务
              this.autoSample();
            }
          });
        }
      });
  }
  prodData: any = {
    cutting_qty: 0,
    order_count: 0,
    qualified_qty: 0,
    sewing_qty: 0,
    transport_qty: 0,
  };
  /**
   * 成衣加工进度数据
   */
  getProdProgress(io_uuid: string) {
    this._service.getProdProgress(io_uuid).subscribe((res: any) => {
      if (res.code === 200) {
        this.prodData = res.data;
      }
    });
  }
  // 保存
  save() {
    this.commit(false);
  }
  // 返回
  back() {
    this._router.navigate(['/outsourcing-manage/garment-outsourcing/list']);
  }
  isCancelOrder = true; // true 可以取消订单，false: 不可以取消订单。页面加载循环info时记录是否可以取消订单状态
  // 取消订单
  cancelOrder() {
    if (!this.isCancelOrder) {
      this._message.error(this.translateKey('outsourcingMessage.工厂已接单，无法取消订单'));
      return;
    }
    this.outsourcingService
      .confirmDialog(
        `${this.translateKey('outsourcingMessage.确定')}<span class="red-mark">${this.translateKey(
          'outsourcingMessage.取消'
        )}</span>${this.translateKey('outsourcingMessage.订单？')}`,
        ' ',
        false,
        true
      )
      .afterClose.subscribe((confirm) => {
        if (confirm) {
          this._service.cancel({ id: this.order_id, cache: true, production_type: 1 }).subscribe((res) => {
            if (res.code === 200) {
              this._message.success(this.translateKey('outsourcingMessage.取消成功'));
              this._service.batchCreateProductionTable({ io_out_sourcing_ids: [this.order_id], operation_type: 2 }).subscribe((res) => {});
              this.getDetail();
              this._service.eventEmitter.emit('refresh');
            }
          });
        }
      });
  }
  // 编辑详情
  edit() {
    this.editMode = 'edit';
  }
  // 二次确认是否返回
  canLeave() {
    if (this.editMode === 'add') {
      return this.detailForm.dirty ? false : true;
    } else {
      return !(this.editMode === 'edit' && (this.detailForm.dirty || this.factoryDataChange));
    }
  }
  ioDefaultValue: any;
  extraOptions: { label: any; value: any; hide: boolean; active?: boolean; disabled?: boolean }[] = [];
  isChangeIoId = false; // 用户是否切换了大货单号，切换了需要删除加工厂id,po_lines_id
  // 切换IO
  async handleChangeValueIo(event: any) {
    const ioId = this.detailForm.get('io_id_local').value;
    const ioCode = this.detailForm.get('io_code').value;
    if (!this.flcUtilService.isNilOrEmptyStr(ioId)) {
      const title = `${this.translateKey('outsourcingMessage.确定')}<span class="red-mark">${this.translateKey(
        'outsourcingMessage.切换'
      )}</span>${this.translateKey('outsourcingMessage.大货单号？')}`;
      const content = this.translateKey('outsourcingMessage.切换大货单后所有已分配的数据将会清空');
      this.outsourcingService.confirmDialog(title, content).afterClose.subscribe(async (val) => {
        if (val) {
          this.detailForm.get('io_id_local').setValue(event.value);
          this.detailForm.get('io_code').setValue(event?.selectLine?.label);
          if (!this.flcUtilService.isNilOrEmptyStr(event.value)) {
            await this.getIoDetail(event.value);
            this.showMainPanel = true;
          } else {
            this.showMainPanel = false;
          }
          this.isChangeIoId = true;
        } else {
          this.detailForm.get('io_id_local').setValue(ioId);
          this.detailForm.get('io_id').setValue(ioId);
          this.detailForm.get('io_code').setValue(ioCode);
        }
        this.ioDefaultValue = { label: this.detailForm.get('io_code')?.value, value: this.detailForm.get('io_id_local')?.value };
      });
    } else {
      this.detailForm.get('io_id_local').setValue(event.value);
      this.showMainPanel = true;
      await this.getIoDetail(event.value);
    }
  }
  // 选择了IO 获取大货单详情
  getIoDetail(id: string) {
    return new Promise((reslove, rejects) => {
      const payload = { id, cache: false, production_type: 1 };
      this._service.ioDetail(payload).subscribe((res) => {
        if (res.code === 200) {
          this.io_basic = res?.data?.io_basic;
          this.io_lines = res?.data?.io_lines;
          this.outsourcingService.io_uuid = this.io_basic?.io_uuid;
          // 给po_lines设置po_id
          console.log('🔍 [DEBUG] getIoDetail - 开始处理 pos 数据');

          const zeroQtyLines: number[] = [];

          res?.data?.pos.forEach((po: Po) => {
            po.po_basic.deletable = true;

            po.po_lines.forEach((line: Line) => {
              line.deletable = true;
              line['po_id'] = po?.po_basic?.id;
              line.extraParams = {
                cellEditable: true,
              };

              if (line.qty === 0) {
                zeroQtyLines.push(line.id);
                line.qty = null;
              }
            });
          });

          if (zeroQtyLines.length > 0) {
            console.log('🔍 [DEBUG] getIoDetail - 将 qty=0 设置为 null 的 line IDs:', zeroQtyLines);
          }

          console.log('🔍 [DEBUG] getIoDetail - 处理完成');
          this.detailForm.get('style_code').setValue(this.io_basic.style_code);
          this.detailForm.get('style_code_uuid').setValue(this.io_basic.style_code_uuid);
          this.pos = res?.data?.pos;
          this.outsourcingService.io_lines = this.outsourcingService.deepclone(res?.data?.io_lines);
          this.outsourcingService.pos = this.outsourcingService.deepclone(res?.data?.pos);
          this.detailForm.get('factorys').clear();
          this.outsourcingService.surplusPos = this.outsourcingService.serialNumberLines(this.outsourcingService.deepclone(res?.data?.pos));
          this.outsourcingService.surplusLines = this.outsourcingService.serialLines(
            this.outsourcingService.getLines(this.outsourcingService.surplusPos)
          );
          this.addFactoryItem();
          reslove(true);
        } else {
          rejects(false);
        }
      });
    });
  }

  // 新增一个 加工厂，将剩余未分配数据填充进去
  /**
   *
   * @param index       给那个加工厂添加私有pos lines数据
   * @param onlyAddFactory 是否仅创建加工厂 不添加数据 false 仅添加加工厂，true 加工厂+数据
   */
  addFactoryItem() {
    const index = this.detailForm.get('factorys').length;
    const bulkOrderCode = this.autoFillBulkcode(null, index);
    const factory: FormGroup = this.fb.group({
      deletable: [true],
      factory_archive_code: [null, []],
      factory_archive_id: [null, []],
      factory_short_name: [null, [this.repeatFactoryValidator()]],
      factory_code: [null, []],
      factory_name: [null],
      factory_id: [null],
      bulk_code: [bulkOrderCode, [Validators.required]],
      lines: [[]],
      unit_price: [null, [Validators.required, this._validator.greaterThanValidator(0)]],
      tax_rate: ['0', [Validators.required, this._validator.greaterThanValidator(-1)]],
      tax_price: [null, [Validators.required, this._validator.greaterThanValidator(0)]],
      finish: [null],
      check_type: [CheckTypeEnum.partCheck, [Validators.required]], // 默认选中抽检
      merchandiser_id: [null],
      merchandiser_name: [null],
      check_radio: [null],
      qc: [null],
      qc_id: [null],
      loss_counts_v2: [null, {}],
      line_no: [null],
      line_name: [null],
      plan_time: [null],
      remark: [null],

      stage_ids: [null, [Validators.required]], // 工段 默认全选
      stage_list: [[]],
      part_names: [null], // 部位
      advance_payment_ratio: [null], //  预付款比例
      advance_payment_money: [null], //  预付款金额
      payment_method: [null], //  付款方式
      payment_method_id: [null], //  付款方式id
      settlement_method: [null], //  结算方式
      settlement_method_id: [null], //  结算方式id
      factory_po_due_times: [null, {}], // 交付日期
    });
    // 向加工厂数组中添加新的加工厂
    this.detailForm.get('factorys').push(factory);
    this.factoryItem.toArray().forEach((item) => {
      item.setToggle(false);
    });
  }

  // Tool
  /**
   * 初始化表单配置项
   */
  initEditorInfoDef() {
    const io_code = {
      type: 'select',
      code: 'io_id_local',
      labelKey: 'io_code',
      valueKey: 'io_id',
      label: '订单需求号',
      required: true,
      nzSpan: this.editMode === 'add' ? 12 : 8,
    };
    const status = {
      type: 'text',
      code: 'status',
      label: '状态',
      formater: this.setStyle,
    };

    const biz_user_emp = {
      type: 'text',
      code: 'biz_user_name',
      label: '业务员',
      nzSpan: 8,
    };
    const employee_name = {
      type: 'text',
      code: 'employee_name',
      label: '负责人',
      nzSpan: 8,
    };
    // this.detailFormConfig = this.editMode === 'add' ? [io_code] : [io_code, status];
    this.detailFormConfig = this.editMode === 'add' ? [io_code] : [io_code, biz_user_emp, employee_name];
  }
  setStyle(item: any) {
    const styleObj: any = {};
    switch (item.order_status) {
      case OrderStatus.toSubmit: // 待提交
        styleObj.color = '#138AFF';
        break;
      case OrderStatus.toAudit: // 待审核
        styleObj.color = '#FB6401';
        break;
      case OrderStatus.toModifyAudit: // 修改待审核
        styleObj.color = '#FB6401';
        break;
      case OrderStatus.modifyAuditReturn: // 修改审核未通过
        styleObj.color = '#FB6401';
        break;
      case OrderStatus.toModify: // 待修改
        styleObj.color = '#FF4A1D';
        break;
      case OrderStatus.cancelled: // 已取消
        styleObj.color = '#97999C';
        break;
      default:
        styleObj.color = '#515661';
        break;
    }
    return styleObj;
  }
  initEditorFormValidation() {
    this.detailForm = this.fb.group({
      id: [null, []],
      io_id_local: [null],
      io_id: [null, [Validators.required]],
      io_code: [null], // 回显大货单号使用
      style_code: [null],
      style_code_uuid: [null],
      factorys: new FormArray([]),
      delete_factory_infos: [[]], // 记录删除调的加工厂id
    });
  }
  get factorys() {
    return this.detailForm.get('factorys') as FormArray;
  }
  translateKey(key: string): string {
    return this.translatePipe.transform(key);
  }

  /** 重新自动打样接口 */
  autoSample() {
    const payload = { io_out_sourcing_id: this.orderDetail['id'] };
    return this.http.post<any>('/service/scm/sample_order/auto_sample', payload).subscribe(() => {});
  }

  factory_options: any[] = [];
  private getFactorysOptions() {
    this._service.getFactorysOptions().subscribe((res) => {
      this.factory_options = res.data.option_list;
    });
  }

  private validatorRepeatFactiory(res: any, reset = false) {
    const factoryList = this.detailForm.get('factorys')?.value || [];
    let is_repeat = false;
    let repeat_factory_name = '';
    let repeat_line_name = '';
    factoryList.forEach((item: any) => {
      if (!item.factory_code) {
        return;
      }
      let repeat = false;
      const _repeat_list = factoryList.filter((val: any) => val.factory_code === item.factory_code);
      const factory_option = this.factory_options.find((val: any) => val.label === item.factory_short_name);
      // 同一工厂同一产线重复报错
      if (factory_option?.order_distribute_type === 2) {
        if (!item.line_no) return;
        const _list = _repeat_list.filter((_item: any) => _item.line_no === item.line_no);
        repeat = _list.length > 1;
        if (repeat) repeat_line_name = item.line_name;
      } else {
        repeat = _repeat_list.length > 1;
      }
      if (repeat) {
        repeat_factory_name = item.factory_short_name;
        if (!reset) return true;
        setTimeout(() => {
          this.detailForm.get('factorys').controls[res.factoryIndex].reset({
            factory_archive_code: res.preValue.factory_archive_code,
            factory_archive_id: res.preValue.factory_archive_id,
            factory_short_name: res.preValue.factory_short_name,
            factory_name: res.preValue.factory_name,
            factory_code: res.preValue.factory_code,
            factory_id: res.preValue.factory_id,
            line_no: res.preValue.line_no,
            line_name: res.preValue.line_name,
            deletable: res.preValue.deletable,
            lossCounts: res.preValue.lossCounts,
            // 因为延迟执行，需要取最新的lines数据
            lines: this.outsourcingService.getLines(this.factoryItem.get(res.factoryIndex)!._pos),
          });
        }, 0);
        is_repeat = true;
      }
      return;
    });

    if (is_repeat) {
      this._message.error(`已有加工厂${repeat_factory_name + (repeat_line_name ? `产线${repeat_line_name}` : '')},请重新选择`);
    }

    return is_repeat;
  }

  // 校验加工厂不可重复选择
  repeatFactoryValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const factorys = this.detailForm.get('factorys').value;
      const repeatList = factorys?.filter((item: any) => item?.factory_archive_code === control?.parent?.value?.factory_archive_code);
      if (repeatList?.length > 1) {
        return { duplicated: { message: '加工厂不可重复' } };
      }
      return null;
    };
  }
}

/**
 * string转成number
 * @param input
 * @returns
 */
const stringToNum = (input: any) => {
  if (isNil(input)) {
    return null;
  } else if (isNaN(Number(input))) {
    return 0;
  }
  return Number(input);
};
