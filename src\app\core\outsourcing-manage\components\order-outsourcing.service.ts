import { Injectable, EventEmitter } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { FlcModalService } from 'fl-common-lib';
import { NzModalService } from 'ng-zorro-antd/modal';
import { Line, Po, Pos } from './models/order-outsourcing-interface';
import { OrderNotpassReasonComponent } from './order-notpass-reason/order-notpass-reason.component';

@Injectable()
export class OrderOutsourcingService {
  constructor(private _flcModalService: FlcModalService, private modalService: NzModalService, private _http: HttpClient) {}

  confirmDialog(title: string, contentStr?: string, strongConfirm = false, showIcon = true) {
    return this._flcModalService.confirmCancel({
      showSubContent: true,
      subContent: contentStr || '此操作不可撤回，数据将不会保存备份',
      content: title,
      contentCenter: false,
      btnCenter: false,
      strongConfirm: strongConfirm,
      showIcon: showIcon,
    });
  }

  confirmDialogWithReason() {
    return this.modalService.create({
      nzContent: OrderNotpassReasonComponent,
      nzWidth: 400,
      nzClosable: false,
      nzWrapClassName: 'flc-confirm-modal',
      nzFooter: null,
    });
  }
  secProcessEventEmitter = new EventEmitter(); // 二次工艺改变时
  garmentEventEmitter = new EventEmitter(); // 成衣改变时
  changeValue = new EventEmitter();
  changePoLineEmitter = new EventEmitter(); // 单独更新某个po表格时使用
  // 初始Pos
  // 剩余未分配pos
  set surplusPos(value: any) {
    this._surplusPos = value;
    this.changeValue.emit({ type: 'surplusPos' });
  }
  get surplusPos() {
    return this._surplusPos;
  }
  _surplusPos: any = [];
  // 剩余未分配合计数据
  set surplusLines(value: any) {
    this._surplusLines = value;
    this.changeValue.emit({ type: 'surplusLines' });
  }
  get surplusLines() {
    return this._surplusLines;
  }
  _surplusLines: any = [];
  // 外发类型
  factory_type = 0; // 1成衣 2二次工艺

  io_uuid = '';
  // 颜色尺码的合计数据
  set io_lines(value: any) {
    // 记录尺码的排序
    this.mapSize = new Map();
    this.mapColor = new Map();
    this.mapSpecSize = new Map();
    let startCode = '';
    let index = 0;
    let specSizeIndex = 0;
    value.sort((a: Line, b: Line) => {
      return a.indexing - b.indexing;
    });
    value.forEach((line: Line) => {
      if (!this.mapSize.has(line?.size_info?.spec_id)) {
        this.mapSize.set(line?.size_info?.spec_id, line?.indexing);
      }
      if (startCode !== line?.color_info?.color_code && !this.mapColor.has(line?.color_info?.color_code)) {
        startCode = line?.color_info?.color_code;
        this.mapColor.set(startCode, ++index);
      }
      if (!this.mapSpecSize.has(line?.size_info?.spec_size)) {
        this.mapSpecSize.set(line?.size_info?.spec_size, ++specSizeIndex);
      }
    });
    this._io_lines = value;
  }
  get io_lines() {
    return this._io_lines;
  }
  _io_lines: any = [];
  mapSize = new Map();
  mapColor = new Map();
  mapSpecSize = new Map();
  // 记录pos中每个po中line的尺码顺序
  set pos(value: Pos) {
    this.mapPosSpecSize = new Map();
    this.mapPosColor = new Map();
    value.forEach((po: Po) => {
      let specSizeIndex = 0;
      let startCode = '';
      let index = 0;
      if (!this.mapPosSpecSize.has(po.po_basic.id)) {
        this.mapPosSpecSize.set(po.po_basic.id, new Map());
      }
      if (!this.mapPosColor.has(po.po_basic.id)) {
        this.mapPosColor.set(po.po_basic.id, new Map());
      }

      po.po_lines.forEach((line: Line) => {
        if (startCode !== line?.color_info?.color_code && !this.mapPosColor.get(po.po_basic.id).has(line?.color_info?.color_code)) {
          startCode = line?.color_info?.color_code;
          this.mapPosColor.get(po.po_basic.id).set(line?.color_info?.color_code, ++index);
        }
      });

      po.po_lines.sort((a: Line, b: Line) => {
        return a.indexing - b.indexing;
      });
      po.po_lines.forEach((line: Line) => {
        if (!this.mapPosSpecSize.get(po.po_basic.id).has(line?.size_info?.spec_size)) {
          this.mapPosSpecSize.get(po.po_basic.id).set(line?.size_info?.spec_size, ++specSizeIndex);
        }
      });
    });
    this.originPos = value;
    this._pos = this.deepclone(value);
  }
  originPos!: Pos;
  _pos!: Pos;
  mapPosSpecSize = new Map();
  mapPosColor = new Map();

  // 当前选中的二次工艺
  nowSelect_extra_process_info: any = {};
  //当前外发厂下标
  factoryIndex?: number;
  public get archiveServiceUrl(): string {
    return '/service/archive/v1';
  }
  /**
   * 加工厂下拉列表
   */
  get plantOptions(): string {
    return '/service/procurement-inventory/outsourcing_management/v1/outsourcing_management/factory-option';
  }

  // 部位下拉列表
  get partOptions(): string {
    return '/service/archive/v1/part/basic_option';
  }

  // 数据字典
  get dictOptionsUrl(): string {
    return '/service/scm/dict_category/dict_option';
  }

  // 大货工段下拉列表
  getStageOptions(payload: any) {
    return this._http.post<any>('/service/order/v1/distribution/stage/option', payload);
  }

  // 重置剩余未分配的值
  resetSurplusPos(event: { value: any; type: 'merge' | 'reset'; factoryIndex?: number }) {
    this.factoryIndex = event.factoryIndex;
    if (event.type === 'merge') {
      this.setSurplusPos([...this.surplusPos, ...event.value]);
    }
    if (event.type === 'reset') {
      this.surplusPos = event.value;
    }
    this.surplusLines = this.serialLines(this.getLines(this.surplusPos));
  }

  // 依据pos获取所有的lines
  getLines(pos: Array<Po>) {
    const lines: any = [];
    pos.forEach((po: any) => {
      po.po_lines.forEach((line: Line) => {
        line['specSizeIndex'] = this.mapSpecSize.get(line.size_info.spec_size);
      });
      lines.push(...po.po_lines);
    });
    return lines.filter((line: Line) => line.qty);
  }
  // 根据pos 计算合计部分数据
  getSurplusLines(pos: Array<Po>) {
    pos = JSON.parse(JSON.stringify(pos));
    // 获取所有的lines
    const lines: any = this.getLines(pos);
    // lines 按颜色尺码id 分组 相同的在一组 用于合并qty
    const gather = lines.reduce((pre: any, next: any) => {
      const key = `${next.color_info.color_id}_${next.size_info.spec_id}`;
      if (!pre[key]) {
        pre[key] = [];
      }
      pre[key].push(next);
      return pre;
    }, {});

    const result: Array<Line> = [];
    Object.keys(gather).forEach((key: string) => {
      // 合并qty
      if (gather[key].length > 1) {
        let qty = 0;
        const po_ids: Array<number> = [];
        gather[key].forEach((line: Line) => {
          qty += line.qty || 0;
          po_ids.push(line.id);
        });
        gather[key][0].qty = qty;
        gather[key][0].po_ids = po_ids;
        result.push(gather[key][0]);
      } else {
        result.push(...gather[key]);
      }
    });

    return [...result];
  }
  // 根据当前pos 重新计算pos lines 去除空，null 的数据
  refreshSurplusPos() {
    const pos: any = [];
    this.surplusPos.forEach((po: Po) => {
      po.po_lines = po.po_lines.filter((line) => line.qty);
      if (po.po_lines.length) {
        pos.push(po);
      }
    });
    this.surplusPos = this.serialNumberLines(pos);
    this.surplusLines = this.getSurplusLines([...pos]);
  }
  // 剔除pos中poLine中qty 是null 的数据
  filterQtyNull(pos: Pos): Pos {
    pos.forEach((po: Po) => {
      po.po_lines = po.po_lines.filter((line) => line.qty);
    });
    return pos.filter((po) => po.po_lines.length); // 过滤po中po_line为空的po
  }
  // 操作表格 删除数据 将原有的surplusPos添加数据 一样的po 需要合并
  setSurplusPos(pos: Array<Po>) {
    // pos = this.filterQtyNull(pos);
    pos.forEach((po: Po) => {
      let hasEqualPo = false; // 记录是否存在相同的po
      this.surplusPos.forEach((s_po: Po) => {
        // 查找到相同的po
        if (po.po_basic.id === s_po.po_basic.id) {
          hasEqualPo = true;
          po.po_lines.forEach((line: Line) => {
            let hasEqualLine = false; // 记录是否存在相同的po
            s_po.po_lines.forEach((s_line: Line) => {
              if (line.id === s_line.id) {
                hasEqualLine = true;
                s_line.qty = line.qty;
              }
            });
            if (!hasEqualLine) {
              s_po.po_lines.push(line);
            }
          });
        }
      });
      // 当前传进来po 与剩余未分配的不存在相同的则直接向surplusPos中添加当前循环的po数据
      if (!hasEqualPo) {
        this.surplusPos.push(po);
      }
    });
    this.surplusPos.sort((a: Po, b: Po) => a.po_basic.id - b.po_basic.id);
    const surplusPos: Array<Po> = this.serialNumberLines([...this.surplusPos]);
    this.surplusPos = [...surplusPos];
  }

  // 排序剩余未分配的数据 保证颜色尺码顺序与大货单下方的颜色尺码顺序一致
  serialNumberLines(pos: Array<Po>, filterNull = true): Array<Po> {
    if (filterNull) {
      pos = this.filterQtyNull(pos);
    }
    pos.forEach((po: Po) => {
      po.po_lines.forEach((line: Line) => {
        line['colorIndex'] = this.mapPosColor.get(po.po_basic.id)?.get(line.color_info.color_code);
        line['specSizeIndex'] = this.mapPosSpecSize.get(po.po_basic.id)?.get(line?.size_info?.spec_size);
      });
      po.po_lines.sort((a: Line, b: Line) => {
        return a['specSizeIndex']! - b['specSizeIndex']!;
      });
    });

    // 重置 indexing
    pos.forEach((po: Po) => {
      let index = 0;
      let spec_id = -1;
      po.po_lines.forEach((line: Line) => {
        if (spec_id !== line.size_info.spec_id) {
          line.indexing = ++index;
          spec_id = line.size_info.spec_id;
        } else {
          line.indexing = index;
        }
      });
    });
    // 对返回颜色进行排序与颜色尺码组一致
    pos.forEach((po: Po) => {
      po.po_lines.sort((a: Line, b: Line) => {
        return a['colorIndex']! - b['colorIndex']!;
      });
    });

    return pos;
  }
  // 根据lines 计算合计部分的数据
  serialLines(lines: Array<Line>) {
    lines = this.deepclone(lines);
    // lines 按颜色尺码id 分组 相同的在一组 用于合并qty
    const gather = lines.reduce((pre: any, next: any) => {
      const key = `${next.color_info.color_id}_${next.size_info.spec_id}`;
      next['colorIndex'] = this.mapColor.get(next.color_info.color_code);
      next['specSizeIndex'] = this.mapSize.get(next.size_info.spec_id);
      if (!pre[key]) {
        pre[key] = [];
      }
      pre[key].push(next);
      return pre;
    }, {});

    const result: Array<Line> = [];
    Object.keys(gather).forEach((key: string) => {
      // 合并qty
      if (gather[key].length > 1) {
        let qty = 0;
        const po_ids: Array<number> = [];
        gather[key].forEach((line: Line) => {
          qty += line.qty || 0;
          po_ids.push(line.id);
        });
        gather[key][0].qty = qty;
        gather[key][0].po_ids = po_ids;
        result.push(gather[key][0]);
      } else {
        result.push(...gather[key]);
      }
    });

    result.sort((a: Line, b: Line) => {
      return a.specSizeIndex! - b.specSizeIndex!;
    });

    let index = 0;
    let spec_id = -1;
    result.forEach((line: Line) => {
      line.extraParams = { cellEditable: true };
      if (spec_id !== line.size_info.spec_id) {
        line.indexing = ++index;
        spec_id = line.size_info.spec_id;
      } else {
        line.indexing = index;
      }
    });
    // 对返回数据的 颜色进行排序与颜色尺码组一致
    result.sort((a: Line, b: Line) => {
      return a['colorIndex']! - b['colorIndex']!;
    });

    return result;
  }

  deepclone(obj: any = {}) {
    return JSON.parse(JSON.stringify(obj));
  }
  // 根据分配订单返回的info，在pos中获取已启用的数据
  getEnabledLines(
    lines: Array<{ id: number; po_id: number; po_line_id: number; factory_id: number; deletable: boolean; qty: number }>,
    pos: Array<Po>
  ) {
    pos = this.deepclone(pos);
    const result: any = { pos: [], po_lines: [] };
    lines.forEach((line: { id: number; po_id: number; po_line_id: number; factory_id: number; deletable: boolean; qty: number }) => {
      const { po_id, po_line_id, id, factory_id, deletable, qty } = line;
      pos.forEach((po: Po) => {
        // 找到相同的po
        if (po?.po_basic?.id === po_id) {
          po.po_lines.forEach((po_line: any) => {
            // 找到同一个po下的po_line
            if (po_line_id === po_line.id) {
              po_line.order_id = id; // 每个line的分配单id
              po_line.factory_id = factory_id; // 加工厂id
              po_line.deletable = deletable; // 重置大货单的deletable 更改为外发单中的deletable
              po_line.qty = qty;
              result.po_lines.push(po_line);
            }
          });
          // 如果po存在就不在向result中添加 但需要更新po_lines 为其添加 order_id  factory_id
          if (!result.pos.some((po: any) => po.po_basic?.id === po_id)) {
            result.pos.push({ po_basic: po.po_basic, po_lines: this.deepclone(po.po_lines) });
          } else {
            // 更新po_lines
            result.pos.forEach((resultPo: Po) => {
              if (resultPo.po_basic?.id === po_id) {
                resultPo.po_lines = this.deepclone(po.po_lines);
              }
            });
          }
        }
      });
    });

    // 重置po中line的qty为null.再循环po_lines更新qty
    result.pos.forEach((po: Po) => {
      po.po_lines.forEach((line: Line) => {
        line.qty = null;
        result.po_lines.forEach((_line: Line) => {
          if (line.id === _line.id) {
            line.qty = _line.qty;
          }
        });
      });
      po.po_basic.deletable = !po.po_lines.some((line: Line) => {
        return line.deletable === false && line.qty !== null;
      });
    });
    return result;
  }
  // 根据pos 去_pos中遍历line.将所有的line 重置为null,若pos中存在line和覆盖_pos的
  // 用于新加工厂 添加剩余未分配数据 显示全量数据
  getPoAllLine(pos: Pos) {
    console.log('🔍 [DEBUG] getPoAllLine 开始执行');
    console.log('🔍 [DEBUG] 输入参数 pos:', JSON.stringify(pos, null, 2));
    console.log('🔍 [DEBUG] 当前 _pos:', JSON.stringify(this._pos, null, 2));

    const result: Pos = [];
    const _pos = this.deepclone(this._pos);

    console.log('🔍 [DEBUG] 深拷贝后的 _pos:', JSON.stringify(_pos, null, 2));

    _pos.forEach((_po: Po, _poIndex: number) => {
      console.log(`🔍 [DEBUG] 处理 _po[${_poIndex}], id: ${_po.po_basic.id}`);

      pos.forEach((po: Po, poIndex: number) => {
        console.log(`🔍 [DEBUG] 比较 pos[${poIndex}], id: ${po.po_basic.id}`);

        if (po.po_basic.id === _po.po_basic.id) {
          console.log(`🔍 [DEBUG] ✅ PO ID 匹配: ${po.po_basic.id}`);
          _po.po_basic.deletable = po.po_basic.deletable;

          _po.po_lines.forEach((_line: Line, _lineIndex: number) => {
            const originalQty = _line.qty;
            console.log(`🔍 [DEBUG] 处理 _line[${_lineIndex}], id: ${_line.id}, 原始qty: ${originalQty}`);

            // 先重置为 null
            _line.qty = null;
            let matched = false;

            po.po_lines.forEach((line: Line, lineIndex: number) => {
              console.log(`🔍 [DEBUG] 比较 line[${lineIndex}], id: ${line.id}, qty: ${line.qty}`);

              if (line.id === _line.id) {
                console.log(`🔍 [DEBUG] ✅ LINE ID 匹配: ${line.id}`);
                console.log(`🔍 [DEBUG] 恢复数据 - qty: ${line.qty}, _qty: ${line._qty}`);

                _line._qty = line._qty;
                _line.qty = line.qty;
                _line.order_id = line.order_id;
                _line.deletable = line.deletable;
                _line.factory_id = line.factory_id;
                _line.extraParams = line.extraParams;
                matched = true;
              }
            });

            if (!matched) {
              console.log(`🔍 [DEBUG] ❌ 未找到匹配的 line，id: ${_line.id}, qty 保持为 null`);
              console.log(
                `🔍 [DEBUG] 可用的 line ids:`,
                po.po_lines.map((l) => l.id)
              );
            } else {
              console.log(`🔍 [DEBUG] ✅ 匹配成功，最终qty: ${_line.qty}`);
            }
          });
          result.push(_po);
        } else {
          console.log(`🔍 [DEBUG] ❌ PO ID 不匹配: ${po.po_basic.id} !== ${_po.po_basic.id}`);
        }
      });
    });

    console.log('🔍 [DEBUG] getPoAllLine 执行完成');
    console.log('🔍 [DEBUG] 返回结果:', JSON.stringify(result, null, 2));

    return result;
  }
  // 页面销毁重置service中的数据
  resetData() {
    this.surplusLines = [];
    this.surplusPos = [];
    this.io_lines = [];
    this._pos = [];
    this.factory_type = 0;
    this.mapSize = new Map();
    this.mapColor = new Map();
    this.mapSpecSize = new Map();
    this.mapPosSpecSize = new Map();
  }

  /**
   * 根据大货单的po顺序对每一个外发厂下的po进行排序
   */
  serialPos(pos: Pos) {
    const originalPoIds = this._pos?.map((po: Po) => {
      return po.po_basic.id;
    });
    pos?.forEach((po: Po) => {
      const poIndex = originalPoIds.indexOf(po.po_basic.id);
      if (poIndex !== -1) {
        po.po_basic.indexing = poIndex;
      }
    });
    return pos?.sort((a: Po, b: Po) => {
      return a.po_basic.indexing - b.po_basic.indexing;
    });
  }
}
